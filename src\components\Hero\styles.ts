import styled, { keyframes } from 'styled-components';

const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const blinkCursor = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
`;

export const HeroSection = styled.section`
  height: 100vh;
  min-height: 700px;
  padding: 0;
  position: relative;
  background-color: var(--black);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  color: var(--white);
  width: 100%;
  
  &.animated-gradient {
    background: linear-gradient(-45deg, var(--black), #1a1a1a, #333333, #1a1a1a);
    background-size: 400% 400%;
  }
  
  @media (max-width: 768px) {
    min-height: 600px;
    background: linear-gradient(-45deg, #121212, #1a1a1a, #282828, #1a1a1a);
  }
  
  @media (max-width: 480px) {
    min-height: 100vh;
    background: linear-gradient(-45deg, #121212, #1a1a1a, #282828, #1a1a1a);
  }
`;

export const HeroOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 0.8) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 1;
  
  @media (max-width: 768px) {
    background: linear-gradient(
      45deg,
      rgba(0, 0, 0, 0.85) 0%,
      rgba(0, 0, 0, 0.7) 50%,
      rgba(0, 0, 0, 0.5) 100%
    );
  }
`;

export const GridPattern = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
  z-index: 1;
  opacity: 0.5;
  
  @media (max-width: 768px) {
    opacity: 0.7;
    background-size: 20px 20px;
    background-image: linear-gradient(rgba(255, 255, 255, 0.08) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(255, 255, 255, 0.08) 1px, transparent 1px);
  }
`;

export const HeroContent = styled.div`
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 900px;
  padding: 0 2rem;
  margin: 0;
  margin-left: 0;
  margin-top: 2rem;
  animation: ${fadeIn} 1s ease forwards;
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  
  @media (max-width: 768px) {
    padding: 0 1.5rem;
    margin-top: 1rem;
  }
`;

export const HeroTitle = styled.h1`
  font-size: 4.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  letter-spacing: -1px;
  position: relative;
  text-align: left;
  
  @media (max-width: 768px) {
    font-size: 3rem;
  }
  
  @media (max-width: 480px) {
    font-size: 2.5rem;
  }
`;

export const Highlight = styled.span`
  color: var(--white);
  position: relative;
  display: inline-block;
  
  &::after {
    content: none;
  }
`;

export const TypedText = styled.div`
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 2rem;
  border-right: none;
  animation: none;
  text-align: left;
  
  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
`;

export const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
  max-width: 600px;
  opacity: 0.9;
  text-align: left;
  
  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
`;

export const ButtonContainer = styled.div`
  display: flex;
  gap: 1.5rem;
  align-self: flex-start;
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }
`;

export const PrimaryButton = styled.button`
  background-color: var(--white);
  color: var(--black);
  border: none;
  border-radius: 0;
  padding: 1rem 2.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
  width: fit-content;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: var(--black);
    transition: all 0.3s ease;
    z-index: -1;
  }
  
  &:hover {
    color: var(--white);
    transform: var(--button-hover);
    box-shadow: var(--box-shadow);
    
    &::before {
      left: 0;
    }
  }
  
  @media (max-width: 480px) {
    width: 100%;
    text-align: center;
  }
`;

export const SecondaryButton = styled.button`
  background-color: transparent;
  color: var(--white);
  border: 1px solid var(--white);
  border-radius: 0;
  padding: 1rem 2.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  z-index: 1;
  width: fit-content;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: var(--white);
    transition: all 0.3s ease;
    z-index: -1;
  }
  
  svg {
    transition: transform 0.3s ease;
  }
  
  &:hover {
    color: var(--black);
    transform: var(--button-hover);
    box-shadow: var(--box-shadow);
    
    &::before {
      left: 0;
    }
    
    svg {
      transform: translateX(5px);
    }
  }
  
  @media (max-width: 480px) {
    width: 100%;
    text-align: center;
    justify-content: center;
  }
`;

export const ScrollIndicator = styled.div`
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s ease;
  
  &:hover {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
  }
`;

export const ScrollIcon = styled.div`
  font-size: 1.5rem;
  color: var(--white);
  
  & > svg {
    font-size: 1.5rem;
    color: var(--white);
  }
`;

export const EffectsContainer = styled.div`
  position: absolute;
  right: 12rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  z-index: 3;
  
  @media (max-width: 1200px) {
    right: 2rem;
    transform: translateY(-50%) scale(0.8);
    opacity: 0.7;
  }
  
  @media (max-width: 768px) {
    right: 1rem;
    transform: translateY(-50%) scale(0.6);
    opacity: 0.5;
  }
  
  @media (max-width: 480px) {
    display: none;
  }
`;

export const TerminalContainer = styled.div`
  width: 380px;
  height: 220px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 6px;
  overflow: hidden;
  font-family: 'Courier New', monospace;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

export const TerminalHeader = styled.div`
  height: 32px;
  background-color: rgba(30, 30, 30, 0.9);
  display: flex;
  align-items: center;
  padding: 0 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

export const TerminalDot = styled.div<{ color: string }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${props => props.color};
  margin-right: 6px;
`;

export const TerminalTitle = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-left: 10px;
`;

export const TerminalBody = styled.div`
  padding: 15px;
  font-size: 14px;
  color: white;
  line-height: 1.5;
  font-family: 'Courier New', monospace;
  
  .cursor {
    animation: ${blinkCursor} 1s infinite;
  }
`;

export const CodeContainer = styled.div`
  width: 380px;
  height: 340px;
  background-color: rgba(15, 15, 15, 0.9);
  border-radius: 6px;
  overflow: hidden;
  font-family: 'Courier New', monospace;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

export const CodeHeader = styled.div`
  height: 32px;
  background-color: rgba(30, 30, 30, 0.9);
  color: white;
  display: flex;
  align-items: center;
  padding: 0 15px;
  font-size: 13px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

export const CodeBody = styled.div`
  padding: 15px;
  font-size: 13px;
  color: white;
  line-height: 1.6;
  height: calc(100% - 32px);
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(30, 30, 30, 0.2);
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(100, 100, 100, 0.4);
    border-radius: 3px;
  }
`; 