import { createGlobalStyle, keyframes } from 'styled-components';

// Animações para efeitos modernos
const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const slideUp = keyframes`
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
`;

const rotate = keyframes`
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
`;

const moveBackgroundGradient = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

export const GlobalStyles = createGlobalStyle`
  :root {
    /* Paleta moderna preto e branco */
    --primary-color: #000000;
    --secondary-color: #1a1a1a;
    --accent-color: #444444;
    --background-color: #ffffff;
    --text-color: #000000;
    --light-text-color: #555555;
    --white: #ffffff;
    --black: #000000;
    --gray-100: #f5f5f5;
    --gray-200: #e0e0e0;
    --gray-300: #cccccc;
    --gray-400: #999999;
    --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06);
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --hover-effect: translateY(-3px);
    --button-hover: translateY(-3px);
    --card-hover: translateY(-10px);
  }
  
  /* Reset básico - aplicado para todos os elementos */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  /* Configurações gerais */
  html, body {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
    width: 100%;
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
  }
  
  body {
    font-family: 'Inter', 'Segoe UI', Roboto, Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
  }
  
  a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition-fast);
    position: relative;
  }
  
  ul {
    list-style: none;
  }
  
  button, input, textarea {
    font-family: inherit;
  }
  
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
  }
  
  section {
    padding: 5rem 0;
    position: relative;
  }
  
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
    margin-bottom: 1rem;
    font-weight: 700;
  }

  /* Classes de animação */
  .fade-in {
    animation: ${fadeIn} 1s ease forwards;
  }

  .slide-up {
    animation: ${slideUp} 0.8s ease forwards;
  }

  /* Animação para imagens */
  img {
    transition: var(--transition-normal);
  }

  img:hover {
    transform: scale(1.03);
  }

  /* Efeito hover para cards */
  .card {
    transition: var(--transition-normal);
  }

  .card:hover {
    transform: var(--card-hover);
    box-shadow: var(--box-shadow);
  }

  /* Pulsação para elementos de destaque */
  .pulse {
    animation: ${pulse} 2s infinite ease-in-out;
  }
  
  /* Rotação para elementos tecnológicos */
  .rotate {
    animation: ${rotate} 10s linear infinite;
  }
  
  /* Fundo gradiente animado */
  .animated-gradient {
    background: linear-gradient(-45deg, var(--black), #1a1a1a, #333333, #1a1a1a);
    background-size: 400% 400%;
    animation: ${moveBackgroundGradient} 15s ease infinite;
  }

  /* Efeitos para o aplicativo React */
  .react-app-container {
    width: 100%;
    overflow-x: hidden;
  }

  .effects-container {
    position: absolute;
    z-index: -1;
    pointer-events: none;
  }

  /* Suavização para efeitos de header quando há rolagem */
  .scrolled .header-effect {
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  }

  @media (max-width: 768px) {
    h1 {
      font-size: 2.25rem;
    }

    h2 {
      font-size: 1.75rem;
    }

    h3 {
      font-size: 1.5rem;
    }
    
    section {
      padding: 3rem 0;
    }
    
    .container {
      padding: 0 1rem;
    }

    .animated-gradient {
      background: linear-gradient(-45deg, #121212, #1a1a1a, #282828, #1a1a1a);
      background-size: 300% 300%;
      animation: ${moveBackgroundGradient} 10s ease infinite;
    }
  }
  
  @media (max-width: 480px) {
    html, body {
      font-size: 15px;
    }
    
    h1 {
      font-size: 2rem;
    }
    
    h2 {
      font-size: 1.5rem;
    }
    
    h3 {
      font-size: 1.25rem;
    }
    
    section {
      padding: 2.5rem 0;
    }
  }
`;

export default GlobalStyles; 