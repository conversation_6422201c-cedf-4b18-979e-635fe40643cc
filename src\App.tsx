import React, { useEffect } from 'react';
import { ThemeProvider } from 'styled-components';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { motion, AnimatePresence } from 'framer-motion';

import Header from './components/Header';
import Hero from './components/Hero';
import Services from './components/Services';
import Projects from './components/Projects';
import Footer from './components/Footer';
import GlobalStyles from './styles/globals';

// Tema light (único)
const theme = {
  colors: {
    primary: '#000000',
    secondary: '#1a1a1a',
    accent: '#444444',
    background: '#ffffff',
    text: '#000000',
    lightText: '#555555',
    white: '#ffffff',
    black: '#000000',
  }
};

const App: React.FC = () => {
  useEffect(() => {
    // Inicializa AOS com configurações avançadas
    AOS.init({
      duration: 800,
      once: false,
      easing: 'ease-in-out',
      mirror: true,
      disable: 'mobile'
    });
    
    // Removemos o código que pode interferir com elementos fora do React app
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      // Adiciona classe quando o scroll passa de certo ponto
      if (scrollTop > 100) {
        document.body.classList.add('scrolled');
      } else {
        document.body.classList.remove('scrolled');
      }

      // Limitando o efeito parallax apenas para elementos dentro da aplicação React
      const reactRoot = document.getElementById('root');
      if (reactRoot) {
        reactRoot.querySelectorAll('.parallax').forEach((el) => {
          const element = el as HTMLElement;
          const speed = element.getAttribute('data-speed') || '0.5';
          const yPos = -(scrollTop * parseFloat(speed));
          element.style.transform = `translateY(${yPos}px)`;
        });
      }
    };

    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="react-app-container"
          style={{ 
            width: '100%', 
            overflow: 'hidden', 
            display: 'flex', 
            flexDirection: 'column', 
            minHeight: '100vh' 
          }}
        >
          <Header />
          <main style={{ width: '100%', flex: '1 0 auto' }}>
            <Hero />
            <Services />
            <Projects />
          </main>
          <Footer />
        </motion.div>
      </AnimatePresence>
    </ThemeProvider>
  );
};

export default App;
