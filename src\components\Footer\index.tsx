import React, { useState, useEffect, useRef } from 'react';
import * as FaIcons from 'react-icons/fa';
import * as S from './styles';
import logo from '../../logo.png';

// Cores para o efeito de circuito
const CIRCUIT_COLORS = [
  'rgba(0, 128, 255, 0.5)',  // Azul
  'rgba(0, 255, 128, 0.5)',  // Verde
  'rgba(255, 0, 128, 0.5)'   // Magenta
];

// Componente Matrix Effect para o Footer
const FooterMatrixEffect: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Definir tamanho do canvas
    const setCanvasSize = () => {
      if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = 300; // Altura menor para o footer
      }
    };

    setCanvasSize();
    window.addEventListener('resize', setCanvasSize);

    // Tamanho da fonte e caracteres
    ctx.font = '8px monospace'; // Fonte menor para o footer
    const characters = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';

    // Colunas
    const columns = Math.floor(canvas.width / 8);
    const drops: number[] = [];

    // Inicializar posição das gotas
    for (let i = 0; i < columns; i++) {
      drops[i] = Math.random() * -50;
    }

    // Função para desenhar
    const draw = () => {
      if (!ctx || !canvas) return;

      // Fundo semi-transparente para criar efeito de desvanecimento
      ctx.fillStyle = 'rgba(0, 0, 0, 0.08)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Texto verde mais sutil
      ctx.fillStyle = 'rgba(0, 255, 120, 0.15)';

      // Loop através de cada coluna
      for (let i = 0; i < drops.length; i++) {
        // Caractere aleatório
        const text = characters.charAt(Math.floor(Math.random() * characters.length));

        // Posição x = i*tamanho da fonte, y = valor da posição da gota
        ctx.fillText(text, i * 8, drops[i] * 8);

        // Incrementar posição Y em 1 unidade
        drops[i]++;

        // Enviar a gota de volta ao topo após sair da tela
        if (drops[i] * 8 > canvas.height && Math.random() > 0.98) {
          drops[i] = 0;
        }
      }
    };

    // Loop de animação
    const interval = setInterval(draw, 50);

    return () => {
      clearInterval(interval);
      window.removeEventListener('resize', setCanvasSize);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
        opacity: 0.3,
        pointerEvents: 'none'
      }}
    />
  );
};

// Componente TechParticles para o Footer
const FooterTechParticles: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Definir largura e altura do canvas
    const setCanvasSize = () => {
      if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = 300; // Altura menor para o footer
      }
    };

    setCanvasSize();
    window.addEventListener('resize', setCanvasSize);

    // Configuração do mouse
    let mouse = {
      x: null as number | null,
      y: null as number | null,
      radius: 100 // Raio menor para o footer
    };

    // Evento para capturar posição do mouse
    const handleMouseMove = (event: MouseEvent) => {
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        mouse.x = event.clientX - rect.left;
        mouse.y = event.clientY - rect.top;
      }
    };

    // Remover interação do mouse quando sair da área
    const handleMouseOut = () => {
      mouse.x = null;
      mouse.y = null;
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseout', handleMouseOut);

    // Criar partículas
    interface IParticle {
      x: number;
      y: number;
      size: number;
      baseX: number;
      baseY: number;
      density: number;
      speedX: number;
      speedY: number;
      color: string;
      update: () => void;
      draw: () => void;
    }

    const particlesArray: IParticle[] = [];
    const particleCount = 50; // Menos partículas para o footer

    class Particle implements IParticle {
      x: number;
      y: number;
      size: number;
      baseX: number;
      baseY: number;
      density: number;
      speedX: number;
      speedY: number;
      color: string;

      constructor() {
        this.x = Math.random() * (canvas?.width || window.innerWidth);
        this.y = Math.random() * (canvas?.height || 300);
        this.baseX = this.x;
        this.baseY = this.y;
        this.size = Math.random() * 1.5 + 0.3; // Partículas menores
        this.density = Math.random() * 20 + 1;
        this.speedX = Math.random() * 0.3 - 0.15;
        this.speedY = Math.random() * 0.3 - 0.15;
        this.color = `rgba(255, 255, 255, ${Math.random() * 0.15 + 0.05})`; // Mais sutis
      }

      update() {
        // Movimento normal das partículas
        this.x += this.speedX;
        this.y += this.speedY;

        // Interação com o mouse
        if (mouse.x !== null && mouse.y !== null) {
          const dx = mouse.x - this.x;
          const dy = mouse.y - this.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < mouse.radius) {
            const forceDirectionX = dx / distance;
            const forceDirectionY = dy / distance;
            const maxDistance = mouse.radius;
            const force = (maxDistance - distance) / maxDistance;
            const directionX = forceDirectionX * force * this.density;
            const directionY = forceDirectionY * force * this.density;

            this.x += directionX;
            this.y += directionY;
          }
        }

        // Retornar gradualmente à posição original
        const dx = this.baseX - this.x;
        const dy = this.baseY - this.y;
        this.x += dx * 0.02;
        this.y += dy * 0.02;

        // Voltar ao canvas quando sair dos limites
        if (this.x < 0 || this.x > (canvas?.width || window.innerWidth)) {
          this.x = Math.random() * (canvas?.width || window.innerWidth);
          this.baseX = this.x;
        }
        if (this.y < 0 || this.y > (canvas?.height || 300)) {
          this.y = Math.random() * (canvas?.height || 300);
          this.baseY = this.y;
        }
      }

      draw() {
        if (ctx) {
          ctx.fillStyle = this.color;
          ctx.beginPath();
          ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
          ctx.fill();
        }
      }
    }

    // Inicializar partículas
    const init = () => {
      for (let i = 0; i < particleCount; i++) {
        particlesArray.push(new Particle());
      }
    };

    init();

    // Conectar partículas próximas com linhas
    const connect = () => {
      if (!ctx) return;

      const maxDistance = 100; // Distância menor para o footer
      for (let a = 0; a < particlesArray.length; a++) {
        for (let b = a; b < particlesArray.length; b++) {
          const dx = particlesArray[a].x - particlesArray[b].x;
          const dy = particlesArray[a].y - particlesArray[b].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < maxDistance) {
            const opacity = 1 - distance / maxDistance;
            ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.08})`; // Mais sutil
            ctx.lineWidth = 0.3;
            ctx.beginPath();
            ctx.moveTo(particlesArray[a].x, particlesArray[a].y);
            ctx.lineTo(particlesArray[b].x, particlesArray[b].y);
            ctx.stroke();
          }
        }
      }
    };

    // Animar partículas
    const animate = () => {
      if (!ctx || !canvas) return;

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      for (let i = 0; i < particlesArray.length; i++) {
        particlesArray[i].update();
        particlesArray[i].draw();
      }

      connect();
      requestAnimationFrame(animate);
    };

    animate();

    // Limpar
    return () => {
      window.removeEventListener('resize', setCanvasSize);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseout', handleMouseOut);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2,
        pointerEvents: 'none'
      }}
    />
  );
};

// Componente simplificado de efeito para dispositivos móveis
const FooterMobileBackgroundEffect: React.FC = () => {
  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      zIndex: 1,
      opacity: 0.2,
      backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(60, 60, 60, 0.3) 1px, transparent 1px), radial-gradient(circle at 75% 75%, rgba(60, 60, 60, 0.3) 1px, transparent 1px)',
      backgroundSize: '40px 40px',
      pointerEvents: 'none'
    }} />
  );
};

const Footer: React.FC = () => {
  const year = new Date().getFullYear();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const circuitNodesRef = useRef<Array<any>>([]);
  const animationFrameRef = useRef<number | null>(null);

  // Inicializar e animar o efeito de circuito
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Adaptar o tamanho do canvas ao elemento pai
    const resizeCanvas = () => {
      const footerElement = document.querySelector('footer');
      if (footerElement) {
        canvas.width = footerElement.clientWidth;
        canvas.height = footerElement.clientHeight;

        // Reiniciar os nós quando o canvas mudar de tamanho
        initCircuitNodes();
      }
    };

    // Criar nós de circuito com posições aleatórias
    const initCircuitNodes = () => {
      const nodes = [];
      const nodeCount = Math.floor(canvas.width * canvas.height / 15000); // Densidade dos nós

      for (let i = 0; i < nodeCount; i++) {
        nodes.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 3 + 1,
          color: CIRCUIT_COLORS[Math.floor(Math.random() * CIRCUIT_COLORS.length)],
          // Velocidade para movimentação sutil
          vx: (Math.random() - 0.5) * 0.3,
          vy: (Math.random() - 0.5) * 0.3,
          // Conexões para outros nós
          connections: Math.floor(Math.random() * 3) + 1
        });
      }

      circuitNodesRef.current = nodes;
    };

    // Função de animação
    const animate = () => {
      if (!ctx || !canvas) return;

      // Limpar o canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const nodes = circuitNodesRef.current;

      // Desenhar conexões entre os nós
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];

        // Mover o nó sutilmente
        node.x += node.vx;
        node.y += node.vy;

        // Inverter direção quando atingir os limites
        if (node.x <= 0 || node.x >= canvas.width) node.vx *= -1;
        if (node.y <= 0 || node.y >= canvas.height) node.vy *= -1;

        // Desenhar o nó
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.size, 0, Math.PI * 2);
        ctx.fillStyle = node.color;
        ctx.fill();

        // Conectar com outros nós próximos
        for (let j = 0; j < nodes.length; j++) {
          if (i === j) continue;

          const otherNode = nodes[j];
          const distance = Math.sqrt(
            Math.pow(node.x - otherNode.x, 2) +
            Math.pow(node.y - otherNode.y, 2)
          );

          // Desenhar linhas se os nós estiverem próximos
          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(node.x, node.y);
            ctx.lineTo(otherNode.x, otherNode.y);

            // Opacidade baseada na distância
            const opacity = 1 - distance / 100;
            ctx.strokeStyle = node.color.replace('0.5', `${opacity * 0.5}`);
            ctx.lineWidth = 0.5;
            ctx.stroke();
          }
        }
      }

      // Continuar a animação
      animationFrameRef.current = requestAnimationFrame(animate);
    };

    // Inicializar e começar a animação
    resizeCanvas();
    animate();

    // Event listener para redimensionamento
    window.addEventListener('resize', resizeCanvas);

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Detectar se é dispositivo móvel
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 ||
                 /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
    };

    // Verificar no início
    checkMobile();

    // Verificar ao redimensionar
    window.addEventListener('resize', checkMobile);

    // Marcar como carregado após um pequeno atraso
    setTimeout(() => {
      setIsLoaded(true);
    }, 100);

    // Efeito de fade no footer baseado em visibilidade
    const handleFooterAnimation = () => {
      const footerElement = document.querySelector('footer');
      if (footerElement) {
        const rect = footerElement.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight && rect.bottom >= 0;

        if (isVisible) {
          footerElement.classList.add('visible');
        }
      }
    };

    window.addEventListener('scroll', handleFooterAnimation);
    handleFooterAnimation(); // Verificar visibilidade inicial

    return () => {
      window.removeEventListener('scroll', handleFooterAnimation);
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return (
    <S.FooterContainer>
      <S.CircuitCanvas ref={canvasRef} />
      {!isMobile && <FooterMatrixEffect />}
      {isMobile && <FooterMobileBackgroundEffect />}
      {!isMobile && <FooterTechParticles />}

      <div className="container">
        <S.FooterContent>
          <S.FooterLogo isLoaded={isLoaded}>
            <S.LogoImage src={logo} alt="Néri Consultoria" />
          </S.FooterLogo>

          <S.FooterGrid>
            <S.FooterColumn isLoaded={isLoaded} delayIndex={1}>
              <S.FooterTitle>Sobre Nós</S.FooterTitle>
              <S.FooterText>
                Somos uma consultoria de TI especializada em soluções tecnológicas
                personalizadas para empresas de todos os portes. Nossa missão é
                transformar ideias em realidade através de tecnologias inovadoras.
              </S.FooterText>
            </S.FooterColumn>

            <S.FooterColumn isLoaded={isLoaded} delayIndex={2}>
              <S.FooterTitle>Nossos Serviços</S.FooterTitle>
              <S.FooterLinks>
                <S.FooterLink href="#services">Soluções em T.I</S.FooterLink>
                <S.FooterLink href="#services">Landing Pages</S.FooterLink>
                <S.FooterLink href="#services">e-Commerce</S.FooterLink>
                <S.FooterLink href="#services">Artes Visuais</S.FooterLink>
                <S.FooterLink href="#services">RPA & Automação</S.FooterLink>
                <S.FooterLink href="#services">Consultoria Técnica</S.FooterLink>
              </S.FooterLinks>
            </S.FooterColumn>

            <S.FooterColumn isLoaded={isLoaded} delayIndex={3}>
              <S.FooterTitle>Links Úteis</S.FooterTitle>
              <S.FooterLinks>
                <S.FooterLink href="#">Início</S.FooterLink>
                <S.FooterLink href="#services">Serviços</S.FooterLink>
                <S.FooterLink href="#projects">Projetos</S.FooterLink>
                <S.FooterLink href="#">Termos de Serviço</S.FooterLink>
                <S.FooterLink href="#">Política de Privacidade</S.FooterLink>
              </S.FooterLinks>
            </S.FooterColumn>

            <S.FooterColumn isLoaded={isLoaded} delayIndex={4}>
              <S.FooterTitle>Contato</S.FooterTitle>
              <S.ContactInfo>
                <S.ContactItem>
                  <strong>Email:</strong> <EMAIL>
                </S.ContactItem>
                <S.ContactItem>
                  <strong>Telefone:</strong> (19) 98983-0210
                </S.ContactItem>
                <S.ContactItem>
                  <strong>Endereço:</strong> Campinas, SP
                </S.ContactItem>
              </S.ContactInfo>

              <S.SocialLinks>
                <S.SocialLink href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                  {FaIcons.FaFacebookF({})}
                </S.SocialLink>
                <S.SocialLink href="https://instagram.com" target="_blank" rel="noopener noreferrer">
                  {FaIcons.FaInstagram({})}
                </S.SocialLink>
              </S.SocialLinks>
            </S.FooterColumn>
          </S.FooterGrid>
        </S.FooterContent>

        <S.FooterBottom isLoaded={isLoaded} delayIndex={5}>
          <S.Copyright>
            &copy; {year} Néri Consultoria. Todos os direitos reservados.
          </S.Copyright>
        </S.FooterBottom>
      </div>
    </S.FooterContainer>
  );
};

export default Footer;