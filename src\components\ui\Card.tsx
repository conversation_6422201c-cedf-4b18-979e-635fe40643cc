import React from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import { tokens } from '../../styles/tokens';

// Tipos para as variações do card
type CardVariant = 'default' | 'elevated' | 'outlined' | 'ghost';
type CardPadding = 'none' | 'sm' | 'md' | 'lg' | 'xl';

interface CardProps {
  variant?: CardVariant;
  padding?: CardPadding;
  hoverable?: boolean;
  clickable?: boolean;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

// Estilos base do card
const CardBase = styled(motion.div)<CardProps>`
  background-color: ${tokens.colors.background.primary};
  border-radius: ${tokens.borderRadius.lg};
  transition: all ${tokens.transitions.normal};
  position: relative;
  overflow: hidden;
  
  ${({ clickable }) => clickable && css`
    cursor: pointer;
  `}
  
  ${({ hoverable, clickable }) => (hoverable || clickable) && css`
    &:hover {
      transform: translateY(-4px);
      box-shadow: ${tokens.shadows.xl};
    }
  `}
`;

// Variações de estilo
const cardVariants = {
  default: css`
    background-color: ${tokens.colors.background.primary};
    border: 1px solid ${tokens.colors.gray[200]};
    box-shadow: ${tokens.shadows.sm};
  `,
  
  elevated: css`
    background-color: ${tokens.colors.background.primary};
    border: none;
    box-shadow: ${tokens.shadows.lg};
  `,
  
  outlined: css`
    background-color: ${tokens.colors.background.primary};
    border: 1px solid ${tokens.colors.gray[300]};
    box-shadow: none;
  `,
  
  ghost: css`
    background-color: transparent;
    border: none;
    box-shadow: none;
  `
};

// Tamanhos de padding
const cardPadding = {
  none: css`
    padding: 0;
  `,
  
  sm: css`
    padding: ${tokens.spacing[4]};
  `,
  
  md: css`
    padding: ${tokens.spacing[6]};
  `,
  
  lg: css`
    padding: ${tokens.spacing[8]};
  `,
  
  xl: css`
    padding: ${tokens.spacing[10]};
  `
};

// Componente estilizado final
const StyledCard = styled(CardBase)`
  ${({ variant = 'default' }) => cardVariants[variant]}
  ${({ padding = 'md' }) => cardPadding[padding]}
`;

// Componentes auxiliares para estrutura do card
export const CardHeader = styled.div`
  margin-bottom: ${tokens.spacing[4]};
  
  &:last-child {
    margin-bottom: 0;
  }
`;

export const CardTitle = styled.h3`
  font-size: ${tokens.typography.fontSize['2xl']};
  font-weight: ${tokens.typography.fontWeight.bold};
  color: ${tokens.colors.text.primary};
  margin: 0 0 ${tokens.spacing[2]} 0;
  line-height: ${tokens.typography.lineHeight.tight};
`;

export const CardSubtitle = styled.p`
  font-size: ${tokens.typography.fontSize.base};
  color: ${tokens.colors.text.secondary};
  margin: 0;
  line-height: ${tokens.typography.lineHeight.normal};
`;

export const CardContent = styled.div`
  color: ${tokens.colors.text.primary};
  line-height: ${tokens.typography.lineHeight.relaxed};
  
  p {
    margin: 0 0 ${tokens.spacing[4]} 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

export const CardFooter = styled.div`
  margin-top: ${tokens.spacing[6]};
  padding-top: ${tokens.spacing[4]};
  border-top: 1px solid ${tokens.colors.gray[200]};
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: ${tokens.spacing[4]};
  
  &:first-child {
    margin-top: 0;
    padding-top: 0;
    border-top: none;
  }
  
  @media (max-width: ${tokens.breakpoints.sm}) {
    flex-direction: column;
    align-items: stretch;
  }
`;

// Componente Card principal
export const Card: React.FC<CardProps> = ({
  variant = 'default',
  padding = 'md',
  hoverable = false,
  clickable = false,
  children,
  onClick,
  ...props
}) => {
  const handleClick = () => {
    if (clickable && onClick) {
      onClick();
    }
  };

  return (
    <StyledCard
      variant={variant}
      padding={padding}
      hoverable={hoverable}
      clickable={clickable}
      onClick={handleClick}
      whileHover={hoverable || clickable ? { y: -4 } : undefined}
      whileTap={clickable ? { scale: 0.98 } : undefined}
      {...props}
    >
      {children}
    </StyledCard>
  );
};

// Card especializado para serviços
export const ServiceCard = styled(Card)`
  height: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: ${tokens.colors.primary[500]};
    transform: scaleX(0);
    transition: transform ${tokens.transitions.normal};
  }
  
  &:hover::before {
    transform: scaleX(1);
  }
`;

// Card especializado para depoimentos
export const TestimonialCard = styled(Card)`
  position: relative;
  
  &::before {
    content: '"';
    position: absolute;
    top: ${tokens.spacing[4]};
    left: ${tokens.spacing[4]};
    font-size: ${tokens.typography.fontSize['6xl']};
    color: ${tokens.colors.gray[200]};
    font-family: serif;
    line-height: 1;
  }
`;

export default Card;
