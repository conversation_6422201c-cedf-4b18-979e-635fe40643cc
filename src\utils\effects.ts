import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';

// Registrar plugins do GSAP
gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

/**
 * Verifica se o dispositivo é móvel
 */
const isMobileDevice = (): boolean => {
  return window.innerWidth <= 768 || 
         /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * Inicializa todos os efeitos interativos do site
 */
export const initializeInteractiveEffects = (): void => {
  // Verificar tipo de dispositivo
  if (isMobileDevice()) {
    // Aplicar apenas efeitos leves em dispositivos móveis
    setupImageReveal();
    setupTextSplitting();
    setupSmoothScrolling();
  } else {
    // Aplicar todos os efeitos em desktop
    setupMagneticElements();
    setupImageReveal();
    setupTextSplitting();
    setupCursorEffects();
    setupParallaxImages();
    setupSmoothScrolling();
  }
};

/**
 * Efeito magnético para botões e elementos interativos
 */
const setupMagneticElements = (): void => {
  // Não aplica efeito magnético em dispositivos móveis
  if (isMobileDevice()) return;
  
  const magneticElements = document.querySelectorAll('.magnetic');
  
  magneticElements.forEach(el => {
    el.addEventListener('mousemove', function(this: HTMLElement, e: Event) {
      const mouseEvent = e as MouseEvent;
      const target = this;
      const boundingRect = target.getBoundingClientRect();
      
      const x = mouseEvent.clientX - boundingRect.left - boundingRect.width / 2;
      const y = mouseEvent.clientY - boundingRect.top - boundingRect.height / 2;
      
      gsap.to(target, {
        duration: 0.3,
        x: x * 0.3,
        y: y * 0.3,
        ease: 'power2.out'
      });
    });
    
    el.addEventListener('mouseleave', function(this: HTMLElement, e: Event) {
      const target = this;
      
      gsap.to(target, {
        duration: 0.5,
        x: 0,
        y: 0,
        ease: 'elastic.out(1, 0.3)'
      });
    });
  });
};

/**
 * Efeito de revelação de imagens ao scrollar
 */
const setupImageReveal = (): void => {
  const revealImages = document.querySelectorAll('.reveal-image');
  
  revealImages.forEach(img => {
    // Cria o wrapper para o efeito
    const parent = img.parentElement;
    const wrapper = document.createElement('div');
    wrapper.className = 'image-reveal-wrapper';
    
    if (parent) {
      parent.insertBefore(wrapper, img);
      wrapper.appendChild(img);
      
      // Cria o overlay para o efeito de revelação
      const overlay = document.createElement('div');
      overlay.className = 'image-reveal-overlay';
      wrapper.appendChild(overlay);
      
      // Animação com ScrollTrigger
      gsap.fromTo(overlay, 
        { scaleX: 1 },
        {
          scaleX: 0,
          transformOrigin: 'right',
          duration: isMobileDevice() ? 0.7 : 1,
          ease: 'power2.inOut',
          scrollTrigger: {
            trigger: wrapper,
            start: 'top 85%',
            end: 'bottom 25%',
            toggleActions: 'play none none reverse'
          }
        }
      );
      
      // Revela a imagem
      gsap.fromTo(img as HTMLElement, 
        { scale: 1.1, opacity: 0 },
        {
          scale: 1,
          opacity: 1,
          delay: isMobileDevice() ? 0.2 : 0.4,
          duration: isMobileDevice() ? 0.6 : 0.8,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: wrapper,
            start: 'top 85%',
            end: 'bottom 25%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    }
  });
};

/**
 * Efeito de divisão de texto para animações
 */
const setupTextSplitting = (): void => {
  const splitTextElements = document.querySelectorAll('.split-text');
  
  splitTextElements.forEach(textElement => {
    // Obtém o texto original
    const text = textElement.textContent || '';
    const words = text.split(' ');
    
    // Limpa o conteúdo original
    textElement.textContent = '';
    
    // Cria spans para cada palavra
    words.forEach((word, index) => {
      const wordSpan = document.createElement('span');
      wordSpan.className = 'word';
      wordSpan.style.display = 'inline-block';
      wordSpan.style.overflow = 'hidden';
      
      const innerSpan = document.createElement('span');
      innerSpan.className = 'inner-word';
      innerSpan.innerHTML = word + (index < words.length - 1 ? '&nbsp;' : '');
      innerSpan.style.display = 'inline-block';
      
      wordSpan.appendChild(innerSpan);
      textElement.appendChild(wordSpan);
      
      // Animação de cada palavra
      gsap.fromTo(innerSpan,
        { 
          y: '100%',
          opacity: 0 
        },
        {
          y: '0%',
          opacity: 1,
          duration: 0.6,
          delay: index * 0.05,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: textElement,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });
  });
};

/**
 * Efeito personalizado para o cursor
 */
const setupCursorEffects = (): void => {
  // Cria elemento customizado para o cursor
  const cursor = document.createElement('div');
  cursor.className = 'custom-cursor';
  document.body.appendChild(cursor);
  
  // Cursor principal (pequeno)
  const dot = document.createElement('div');
  dot.className = 'cursor-dot';
  cursor.appendChild(dot);
  
  // Círculo externo (maior)
  const circle = document.createElement('div');
  circle.className = 'cursor-circle';
  cursor.appendChild(circle);
  
  // Atualiza a posição do cursor personalizado
  document.addEventListener('mousemove', (e: MouseEvent) => {
    gsap.to(dot, {
      x: e.clientX,
      y: e.clientY,
      duration: 0.1,
      ease: 'power1.out'
    });
    
    gsap.to(circle, {
      x: e.clientX,
      y: e.clientY,
      duration: 0.5,
      ease: 'power2.out'
    });
  });
  
  // Efeito ao passar sobre elementos clicáveis
  const hoverables = document.querySelectorAll('a, button, .btn, .clickable');
  
  hoverables.forEach(el => {
    el.addEventListener('mouseenter', () => {
      cursor.classList.add('hover');
    });
    
    el.addEventListener('mouseleave', () => {
      cursor.classList.remove('hover');
    });
  });
};

/**
 * Rolagem suave aprimorada
 */
const setupSmoothScrolling = (): void => {
  // Links com âncora para rolagem suave
  document.querySelectorAll('a[href^="#"]:not(.no-scroll)').forEach(anchor => {
    anchor.addEventListener('click', function(this: HTMLAnchorElement, e: Event) {
      e.preventDefault();
      
      const targetId = this.getAttribute('href');
      if (targetId && targetId !== '#') {
        const targetElement = document.querySelector(targetId);
        
        if (targetElement) {
          gsap.to(window, {
            duration: 1.2,
            scrollTo: {
              y: targetElement,
              offsetY: 80
            },
            ease: 'power3.inOut'
          });
        }
      }
    });
  });
};

/**
 * Efeito parallax para imagens
 */
const setupParallaxImages = (): void => {
  const parallaxElements = document.querySelectorAll('.parallax');
  
  parallaxElements.forEach(el => {
    const speed = parseFloat(el.getAttribute('data-speed') || '0.5');
    
    ScrollTrigger.create({
      trigger: el,
      start: 'top bottom',
      end: 'bottom top',
      scrub: true,
      onUpdate: (self: any) => {
        const yPos = self.progress * -100 * speed;
        (el as HTMLElement).style.transform = `translateY(${yPos}px)`;
      }
    });
  });
};

// Exporta todas as funções individuais para uso específico
export {
  setupMagneticElements,
  setupImageReveal,
  setupTextSplitting,
  setupCursorEffects,
  setupSmoothScrolling,
  setupParallaxImages
}; 