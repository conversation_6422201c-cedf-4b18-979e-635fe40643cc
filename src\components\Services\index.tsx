import React, { useEffect, useState, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'framer-motion';
import * as FaIcons from 'react-icons/fa';
import { FaArrowRight } from 'react-icons/fa';
import { SectionTitle, SectionSubtitle, Text } from '../ui';
import * as S from './styles';
import { gsap } from 'gsap';

interface ServiceProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  index: number;
}

const ServiceCard: React.FC<ServiceProps> = ({ icon, title, description, index }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.3 });
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (cardRef.current) {
      cardRef.current.classList.add('magnetic');
    }
  }, []);

  const variants = {
    hover: {
      y: -10,
      scale: 1.05,
      boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const iconVariants = {
    normal: { scale: 1, rotate: 0 },
    hover: {
      scale: 1.2,
      rotate: [0, -10, 10, -5, 5, 0],
      transition: {
        duration: 0.6,
        ease: "easeInOut"
      }
    }
  };

  // Adiciona a classe 'reveal-image' para usar o efeito do arquivo effects.ts
  useEffect(() => {
    if (isInView && cardRef.current) {
      cardRef.current.querySelector('.icon-container')?.classList.add('reveal-image');

      // Efeito de texto dividido
      const titleElement = cardRef.current.querySelector('.service-title');
      if (titleElement) {
        titleElement.classList.add('split-text');
      }

      // Adiciona efeito de gradiente animado no hover
      cardRef.current.addEventListener('mouseenter', () => {
        cardRef.current?.classList.add('moving-gradient');
      });

      cardRef.current.addEventListener('mouseleave', () => {
        cardRef.current?.classList.remove('moving-gradient');
      });
    }
  }, [isInView]);

  return (
    <motion.div
      ref={cardRef}
      whileHover="hover"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      variants={variants}
      className="card service-card"
      data-aos="fade-up"
      data-aos-delay={index * 100}
    >
      <S.ServiceCardContainer>
        <motion.div
          className="icon-container"
          variants={iconVariants}
        >
          <S.IconContainer>
            {icon}
            <S.IconBackground
              initial={{ scale: 0 }}
              animate={{ scale: isHovered ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            />
          </S.IconContainer>
        </motion.div>

        <Text size="xl" align="center" style={{ fontWeight: 600, marginBottom: '1rem' }} className="service-title">
          {title}
        </Text>

        <Text align="center" color="secondary" style={{ marginBottom: '1.5rem' }}>
          {description}
        </Text>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: isHovered ? 1 : 0, y: isHovered ? 0 : 20 }}
          transition={{ duration: 0.3 }}
        >
          <S.LearnMoreButton>
            Saiba mais <>{FaArrowRight({ style: { marginLeft: '8px' } })}</>
          </S.LearnMoreButton>
        </motion.div>
      </S.ServiceCardContainer>
    </motion.div>
  );
};

const Services: React.FC = () => {
  const controls = useAnimation();
  const ref = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { once: true });

  useEffect(() => {
    if (isInView) {
      controls.start('visible');

      // Efeito de texto divido para o título
      if (titleRef.current) {
        titleRef.current.classList.add('split-text');
      }

      // Efeito de linha animada abaixo do subtítulo
      if (subtitleRef.current) {
        gsap.fromTo(
          subtitleRef.current.querySelector('.line-animation'),
          { width: '0%' },
          {
            width: '100%',
            duration: 1.5,
            ease: 'power3.out',
            delay: 0.5
          }
        );
      }

      // Efeito de stagger para os cards - ÚNICA animação de entrada
      if (gridRef.current) {
        gsap.fromTo(
          gridRef.current.querySelectorAll('.service-card'),
          { y: 50, opacity: 0 },
          {
            y: 0,
            opacity: 1,
            stagger: 0.1,
            duration: 0.8,
            ease: 'power2.out',
            delay: 0.2
          }
        );
      }
    }
  }, [controls, isInView]);

  const services = [
    {
      icon: FaIcons.FaLaptopCode({}),
      title: 'Soluções em T.I sob medida',
      description: 'Desenvolvimento de software personalizado para atender às necessidades específicas do seu negócio e impulsionar a eficiência operacional.'
    },
    {
      icon: FaIcons.FaMobileAlt({}),
      title: 'Landing Pages',
      description: 'Criação de páginas de destino otimizadas para conversão, aumentando as chances de transformar visitantes em clientes.'
    },
    {
      icon: FaIcons.FaShoppingCart({}),
      title: 'e-Commerce',
      description: 'Desenvolvimento de lojas virtuais completas, seguras e eficientes para vender seus produtos online e expandir seu alcance de mercado.'
    },
    {
      icon: FaIcons.FaPaintBrush({}),
      title: 'Artes Visuais',
      description: 'Criação de identidades visuais, logos, materiais gráficos e designs que comunicam a essência da sua marca de forma efetiva.'
    },
    {
      icon: FaIcons.FaRobot({}),
      title: 'RPA & Automação de Processos',
      description: 'Implementação de soluções de automação para otimizar processos repetitivos, reduzir erros e liberar sua equipe para tarefas estratégicas.'
    },
    {
      icon: FaIcons.FaHeadset({}),
      title: 'Consultoria Técnica em T.I',
      description: 'Assessoria especializada para auxiliar na tomada de decisões relacionadas à tecnologia, infraestrutura e segurança da informação.'
    }
  ];

  return (
    <S.ServicesSection id="services" ref={ref} className="parallax" data-speed="0.1">
      <div className="container">
        <motion.div
          initial="hidden"
          animate={controls}
          variants={{
            hidden: { opacity: 0, y: 50 },
            visible: {
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.6,
                ease: "easeInOut"
              }
            }
          }}
        >
          <SectionTitle ref={titleRef} align="center">Nossos Serviços</SectionTitle>
          <SectionSubtitle ref={subtitleRef} align="center">
            Soluções completas para impulsionar seu negócio
            <span className="line-animation" style={{
              display: 'block',
              height: '3px',
              background: '#000',
              marginTop: '15px',
              width: '0%',
              marginLeft: 'auto',
              marginRight: 'auto'
            }}></span>
          </SectionSubtitle>
        </motion.div>

        <div>
          <S.ServiceGrid ref={gridRef}>
            {services.map((service, index) => (
              <ServiceCard
                key={index}
                icon={service.icon}
                title={service.title}
                description={service.description}
                index={index}
              />
            ))}
          </S.ServiceGrid>
        </div>
      </div>
    </S.ServicesSection>
  );
};

export default Services;