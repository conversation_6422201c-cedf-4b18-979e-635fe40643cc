import styled, { keyframes } from 'styled-components';
import { motion } from 'framer-motion';

const pulseIcon = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
`;

const rotateGradient = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

export const ServicesSection = styled.section`
  background-color: var(--background-color);
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: 0;
  }
`;

export const SectionTitle = styled.h2`
  font-size: 3rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 1rem;
  color: var(--text-color);
  letter-spacing: -1px;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

export const SectionSubtitle = styled.p`
  font-size: 1.25rem;
  text-align: center;
  max-width: 600px;
  margin: 0 auto 5rem;
  color: var(--light-text-color);

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: 4rem;
  }
`;

export const ServiceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-top: 3rem;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
    gap: 2rem;
  }
`;

export const ServiceCardContainer = styled.div`
  background-color: var(--background-color);
  border: 1px solid var(--gray-200);
  border-radius: 0;
  padding: 3rem 2rem;
  transition: all 0.4s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 4px;
    width: 0;
    background-color: var(--primary-color);
    transition: width 0.4s ease;
  }

  &:hover {
    transform: translateY(-15px);
    box-shadow: var(--box-shadow);
    border-color: transparent;
    
    &::before {
      width: 100%;
    }
  }
  
  &.moving-gradient {
    background: linear-gradient(
      -45deg,
      #f9f9f9,
      #ffffff,
      #f5f5f5,
      #fcfcfc
    );
    background-size: 400% 400%;
    animation: ${rotateGradient} 3s ease infinite;
  }
`;

export const IconContainer = styled.div`
  background-color: var(--background-color);
  color: var(--primary-color);
  width: 80px;
  height: 80px;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  
  ${ServiceCardContainer}:hover & {
    background-color: var(--primary-color);
    color: var(--white);
    animation: ${pulseIcon} 0.5s ease-in-out;
  }
`;

export const IconBackground = styled(motion.div)`
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 70%);
  z-index: -1;
`;

export const ServiceTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  transition: color 0.3s ease;
`;

export const ServiceDescription = styled.p`
  color: var(--light-text-color);
  line-height: 1.8;
  font-size: 1rem;
  flex-grow: 1;
`;

export const ServiceLink = styled.a`
  display: inline-flex;
  align-items: center;
  margin-top: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  transition: all 0.3s ease;
  
  svg {
    margin-left: 6px;
    transition: transform 0.3s ease;
  }
  
  &:hover {
    svg {
      transform: translateX(5px);
    }
  }
`;

export const LearnMoreButton = styled.button`
  background: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  margin-top: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
    z-index: -1;
  }
  
  &:hover {
    color: var(--white);
    
    &::before {
      left: 0;
    }
    
    svg {
      transform: translateX(5px);
    }
  }
  
  svg {
    transition: transform 0.3s ease;
  }
`; 