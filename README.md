# NeriConsult - Landing Page de Consultoria de TI

Uma landing page moderna e responsiva para uma consultoria de TI, desenvolvida com React, TypeScript e Styled Components.

## 📋 Características

- **Design Moderno e Responsivo**: Layout adaptável para dispositivos móveis, tablets e desktops.
- **Animações Interativas**: Animações suaves de scroll e interações de elementos utilizando Framer Motion e AOS.
- **Componentes Reutilizáveis**: Arquitetura baseada em componentes para fácil manutenção e extensibilidade.
- **Formulário de Contato Funcional**: Formulário com validação em tempo real e envio real de emails via EmailJS.
- **Efeito Parallax**: Efeito parallax na seção hero para uma experiência visual mais imersiva.
- **SEO Otimizado**: Meta tags, Open Graph e Twitter Cards configurados.
- **Testes Automatizados**: Testes unitários com Jest e Testing Library.

## 🚀 Tecnologias Utilizadas

- **React** - Biblioteca JavaScript para construção de interfaces
- **TypeScript** - Superset tipado de JavaScript
- **Styled Components** - Biblioteca para estilização com CSS-in-JS
- **Framer Motion** - Biblioteca para animações avançadas
- **EmailJS** - Serviço para envio de emails sem backend
- **React Scroll** - Navegação suave entre seções
- **AOS (Animate On Scroll)** - Biblioteca para animações ao rolar a página
- **React Icons** - Pacote de ícones para React
- **Jest & Testing Library** - Framework de testes

## 📦 Estrutura do Projeto

```
/src
  /components
    /Header - Cabeçalho com menu de navegação responsivo
    /Hero - Seção principal com efeito parallax
    /Services - Grade de serviços oferecidos
    /Contact - Formulário de contato com validação
    /Footer - Rodapé com informações de contato e links
  /styles
    globals.ts - Estilos globais e variáveis CSS
  App.tsx - Componente principal
  index.tsx - Ponto de entrada da aplicação
```

## 🔧 Instalação e Uso

1. Clone o repositório:
```bash
git clone https://github.com/seu-usuario/neri-consult.git
cd neri-consult
```

2. Instale as dependências:
```bash
npm install
```

3. Inicie o servidor de desenvolvimento:
```bash
npm start
```

4. Configure o EmailJS (opcional):
```bash
# Copie o arquivo de exemplo
cp .env.example .env.local

# Edite o .env.local com suas credenciais do EmailJS
REACT_APP_EMAILJS_SERVICE_ID=seu_service_id
REACT_APP_EMAILJS_TEMPLATE_ID=seu_template_id
REACT_APP_EMAILJS_PUBLIC_KEY=sua_public_key
```

5. Acesse a aplicação em:
```
http://localhost:3000
```

## 📧 Configuração do EmailJS

Para que o formulário de contato funcione corretamente:

1. Crie uma conta gratuita em [EmailJS](https://www.emailjs.com/)
2. Configure um serviço de email (Gmail, Outlook, etc.)
3. Crie um template de email
4. Obtenha suas credenciais e adicione no arquivo `.env.local`

### Template de Email Sugerido:
```
Assunto: Novo contato do site - {{subject}}

Nome: {{from_name}}
Email: {{from_email}}
Telefone: {{phone}}
Assunto: {{subject}}

Mensagem:
{{message}}
```

## 🎨 Personalização

- **Cores**: Edite as variáveis CSS em `src/styles/globals.ts`
- **Fontes**: Modifique a importação de fontes em `public/index.html`
- **Imagens**: Substitua as imagens em `public/images/`
- **Conteúdo**: Atualize textos e informações de contato nos componentes relevantes

## 📱 Recursos Responsivos

A landing page foi desenvolvida com uma abordagem mobile-first e inclui:

- Menu de navegação colapsável para dispositivos móveis
- Layout adaptativo para todos os tamanhos de tela
- Imagens otimizadas para carregamento rápido
- Estilos específicos para diferentes breakpoints

## 🤝 Contribuição

Contribuições são bem-vindas! Sinta-se à vontade para:

1. Abrir uma issue
2. Enviar um pull request
3. Sugerir melhorias

## 📄 Licença

Este projeto está licenciado sob a licença MIT. Veja o arquivo LICENSE para mais detalhes.
