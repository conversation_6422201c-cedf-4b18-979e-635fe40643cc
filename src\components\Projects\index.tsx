import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaGithub, FaExternalLinkAlt, FaCode, FaLaptopCode, FaMobile, FaDatabase, FaCloud, FaRocket } from 'react-icons/fa';
import * as S from './styles';

// Dados dos projetos (você pode personalizar estes dados)
const projectsData = [
  {
    id: 1,
    title: "Sistema de Gestão Empresarial",
    description: "Plataforma completa para gestão de empresas com módulos de vendas, estoque, financeiro e relatórios avançados.",
    technologies: ["React", "Node.js", "PostgreSQL", "TypeScript"],
    category: "Web Application",
    image: "/images/project1.jpg", // Você pode adicionar imagens depois
    githubUrl: "https://github.com/seu-usuario/projeto1",
    liveUrl: "https://projeto1.com",
    featured: true,
    icon: FaLaptopCode
  },
  {
    id: 2,
    title: "E-commerce Moderno",
    description: "Loja virtual responsiva com sistema de pagamentos, gestão de produtos e painel administrativo completo.",
    technologies: ["Next.js", "Stripe", "MongoDB", "Tailwind CSS"],
    category: "E-commerce",
    image: "/images/project2.jpg",
    githubUrl: "https://github.com/seu-usuario/projeto2",
    liveUrl: "https://projeto2.com",
    featured: true,
    icon: FaMobile
  },
  {
    id: 3,
    title: "API de Automação RPA",
    description: "Sistema de automação robótica de processos para otimização de tarefas repetitivas em empresas.",
    technologies: ["Python", "Selenium", "FastAPI", "Docker"],
    category: "Automation",
    image: "/images/project3.jpg",
    githubUrl: "https://github.com/seu-usuario/projeto3",
    liveUrl: "https://projeto3.com",
    featured: false,
    icon: FaRocket
  },
  {
    id: 4,
    title: "Dashboard Analytics",
    description: "Painel de controle com visualizações de dados em tempo real e relatórios personalizáveis.",
    technologies: ["Vue.js", "D3.js", "Express", "MySQL"],
    category: "Data Visualization",
    image: "/images/project4.jpg",
    githubUrl: "https://github.com/seu-usuario/projeto4",
    liveUrl: "https://projeto4.com",
    featured: false,
    icon: FaDatabase
  },
  {
    id: 5,
    title: "Aplicativo Mobile",
    description: "App nativo para gestão de tarefas com sincronização em nuvem e notificações push.",
    technologies: ["React Native", "Firebase", "Redux", "Expo"],
    category: "Mobile App",
    image: "/images/project5.jpg",
    githubUrl: "https://github.com/seu-usuario/projeto5",
    liveUrl: "https://projeto5.com",
    featured: false,
    icon: FaMobile
  },
  {
    id: 6,
    title: "Infraestrutura Cloud",
    description: "Arquitetura de microserviços escalável com deploy automatizado e monitoramento.",
    technologies: ["AWS", "Docker", "Kubernetes", "Terraform"],
    category: "DevOps",
    image: "/images/project6.jpg",
    githubUrl: "https://github.com/seu-usuario/projeto6",
    liveUrl: "https://projeto6.com",
    featured: true,
    icon: FaCloud
  }
];



const Projects: React.FC = () => {
  const [filter, setFilter] = useState('all');
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  const categories = ['all', 'Web Application', 'E-commerce', 'Mobile App', 'Automation', 'DevOps', 'Data Visualization'];
  
  const filteredProjects = filter === 'all' 
    ? projectsData 
    : projectsData.filter(project => project.category === filter);
  
  const featuredProjects = projectsData.filter(project => project.featured);
  
  return (
    <S.ProjectsSection id="projects">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6 }}
        >
          <S.SectionHeader>
            <S.SectionTitle>
              <FaCode style={{ marginRight: '15px' }} />
              Meus Projetos
            </S.SectionTitle>
            <S.SectionSubtitle>
              Conheça alguns dos projetos que desenvolvi, desde aplicações web até soluções de automação
            </S.SectionSubtitle>
          </S.SectionHeader>
        </motion.div>

        {/* Projetos em Destaque */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <S.FeaturedSection>
            <S.FeaturedTitle>Projetos em Destaque</S.FeaturedTitle>
            <S.FeaturedGrid>
              {featuredProjects.map((project, index) => (
                <S.FeaturedCard
                  key={project.id}
                  as={motion.div}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -10, scale: 1.02 }}
                >
                  <S.ProjectIcon>
                    {React.createElement(project.icon, { size: 24 })}
                  </S.ProjectIcon>
                  <S.ProjectContent>
                    <S.ProjectTitle>{project.title}</S.ProjectTitle>
                    <S.ProjectDescription>{project.description}</S.ProjectDescription>
                    <S.TechStack>
                      {project.technologies.map((tech, techIndex) => (
                        <S.TechTag key={techIndex}>{tech}</S.TechTag>
                      ))}
                    </S.TechStack>
                  </S.ProjectContent>
                  <S.ProjectLinks>
                    <S.ProjectLink href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                      <FaGithub />
                    </S.ProjectLink>
                    <S.ProjectLink href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                      <FaExternalLinkAlt />
                    </S.ProjectLink>
                  </S.ProjectLinks>
                </S.FeaturedCard>
              ))}
            </S.FeaturedGrid>
          </S.FeaturedSection>
        </motion.div>

        {/* Filtros e Todos os Projetos */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <S.FilterSection>
            <S.FeaturedTitle>Todos os Projetos</S.FeaturedTitle>
            <S.FilterButtons>
              {categories.map((category) => (
                <S.FilterButton
                  key={category}
                  active={filter === category}
                  onClick={() => setFilter(category)}
                >
                  {category === 'all' ? 'Todos' : category}
                </S.FilterButton>
              ))}
            </S.FilterButtons>
          </S.FilterSection>

          <S.ProjectsGrid>
            {filteredProjects.map((project, index) => (
              <S.ProjectCard
                key={project.id}
                as={motion.div}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <S.ProjectCategory>{project.category}</S.ProjectCategory>
                <S.ProjectIcon>
                  {React.createElement(project.icon, { size: 20 })}
                </S.ProjectIcon>
                <S.ProjectContent>
                  <S.ProjectTitle>{project.title}</S.ProjectTitle>
                  <S.ProjectDescription>{project.description}</S.ProjectDescription>
                  <S.TechStack>
                    {project.technologies.map((tech, techIndex) => (
                      <S.TechTag key={techIndex}>{tech}</S.TechTag>
                    ))}
                  </S.TechStack>
                </S.ProjectContent>
                <S.ProjectLinks>
                  <S.ProjectLink href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                    <FaGithub />
                  </S.ProjectLink>
                  <S.ProjectLink href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                    <FaExternalLinkAlt />
                  </S.ProjectLink>
                </S.ProjectLinks>
              </S.ProjectCard>
            ))}
          </S.ProjectsGrid>
        </motion.div>
      </div>
    </S.ProjectsSection>
  );
};

export default Projects;
