import React, { useState } from 'react';
import { motion } from 'framer-motion';
import * as FaIcons from 'react-icons/fa';
import emailjs from '@emailjs/browser';
import { Card, Input, TextArea, Button, SectionTitle, SectionSubtitle, Text } from '../ui';
import * as S from './styles';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  const [formErrors, setFormErrors] = useState({
    name: '',
    email: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { name: '', email: '', message: '' };

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
      valid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
      valid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
      valid = false;
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Mensagem é obrigatória';
      valid = false;
    }

    setFormErrors(newErrors);
    return valid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);

      try {
        // Configuração do EmailJS
        const templateParams = {
          from_name: formData.name,
          from_email: formData.email,
          phone: formData.phone,
          subject: formData.subject,
          message: formData.message,
          to_email: '<EMAIL>'
        };

        // Enviar email usando EmailJS
        await emailjs.send(
          process.env.REACT_APP_EMAILJS_SERVICE_ID || 'YOUR_SERVICE_ID',
          process.env.REACT_APP_EMAILJS_TEMPLATE_ID || 'YOUR_TEMPLATE_ID',
          templateParams,
          process.env.REACT_APP_EMAILJS_PUBLIC_KEY || 'YOUR_PUBLIC_KEY'
        );

        setIsSubmitting(false);
        setIsSubmitted(true);
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: ''
        });

        // Reset success message after 5 seconds
        setTimeout(() => {
          setIsSubmitted(false);
        }, 5000);
      } catch (error) {
        console.error('Erro ao enviar email:', error);
        setIsSubmitting(false);
        // Aqui você pode adicionar uma mensagem de erro para o usuário
        alert('Erro ao enviar mensagem. Tente novamente ou entre em contato diretamente.');
      }
    }
  };

  return (
    <S.ContactSection id="contact">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6 }}
        >
          <SectionTitle align="center">Entre em Contato</SectionTitle>
          <SectionSubtitle align="center">
            Estamos prontos para atender às suas necessidades
          </SectionSubtitle>
        </motion.div>

        <S.ContactContainer>
          <S.ContactInfo>
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <S.ContactTitle>Informações de Contato</S.ContactTitle>
              <S.ContactText>
                Preencha o formulário ou entre em contato diretamente por um dos canais abaixo.
              </S.ContactText>

              <S.ContactInfoList>
                <S.ContactInfoItem>
                  <S.ContactInfoIcon>
                    {FaIcons.FaPhone({})}
                  </S.ContactInfoIcon>
                  <div>
                    <S.ContactInfoLabel>Telefone</S.ContactInfoLabel>
                    <S.ContactInfoValue>(19) 98983-0210</S.ContactInfoValue>
                  </div>
                </S.ContactInfoItem>

                <S.ContactInfoItem>
                  <S.ContactInfoIcon>
                    {FaIcons.FaEnvelope({})}
                  </S.ContactInfoIcon>
                  <div>
                    <S.ContactInfoLabel>Email</S.ContactInfoLabel>
                    <S.ContactInfoValue><EMAIL></S.ContactInfoValue>
                  </div>
                </S.ContactInfoItem>

                <S.ContactInfoItem>
                  <S.ContactInfoIcon>
                    {FaIcons.FaMapMarkerAlt({})}
                  </S.ContactInfoIcon>
                  <div>
                    <S.ContactInfoLabel>Endereço</S.ContactInfoLabel>
                    <S.ContactInfoValue>
                      Campinas, SP
                    </S.ContactInfoValue>
                  </div>
                </S.ContactInfoItem>
              </S.ContactInfoList>
            </motion.div>
          </S.ContactInfo>

          <S.ContactForm
            as={motion.form}
            onSubmit={handleSubmit}
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.2 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {isSubmitted ? (
              <S.SuccessMessage>
                {FaIcons.FaPaperPlane({})}
                <h3>Mensagem enviada com sucesso!</h3>
                <p>Entraremos em contato em breve.</p>
              </S.SuccessMessage>
            ) : (
              <>
                <S.InputGroup>
                  <S.FormLabel htmlFor="name">Nome Completo*</S.FormLabel>
                  <S.Input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    error={!!formErrors.name}
                  />
                  {formErrors.name && <S.ErrorText>{formErrors.name}</S.ErrorText>}
                </S.InputGroup>

                <S.InputRow>
                  <S.InputGroup>
                    <S.FormLabel htmlFor="email">Email*</S.FormLabel>
                    <S.Input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      error={!!formErrors.email}
                    />
                    {formErrors.email && <S.ErrorText>{formErrors.email}</S.ErrorText>}
                  </S.InputGroup>

                  <S.InputGroup>
                    <S.FormLabel htmlFor="phone">Telefone</S.FormLabel>
                    <S.Input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                    />
                  </S.InputGroup>
                </S.InputRow>

                <S.InputGroup>
                  <S.FormLabel htmlFor="subject">Assunto</S.FormLabel>
                  <S.Select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                  >
                    <option value="">Selecione um assunto</option>
                    <option value="Soluções em T.I">Soluções em T.I</option>
                    <option value="Landing Pages">Landing Pages</option>
                    <option value="e-Commerce">e-Commerce</option>
                    <option value="Artes Visuais">Artes Visuais</option>
                    <option value="RPA & Automação">RPA & Automação</option>
                    <option value="Consultoria Técnica">Consultoria Técnica</option>
                    <option value="Outros">Outros</option>
                  </S.Select>
                </S.InputGroup>

                <S.InputGroup>
                  <S.FormLabel htmlFor="message">Mensagem*</S.FormLabel>
                  <S.TextArea
                    id="message"
                    name="message"
                    rows={5}
                    value={formData.message}
                    onChange={handleChange}
                    error={!!formErrors.message}
                  />
                  {formErrors.message && <S.ErrorText>{formErrors.message}</S.ErrorText>}
                </S.InputGroup>

                <S.SubmitButton type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Enviando...' : 'Enviar Mensagem'}
                </S.SubmitButton>
              </>
            )}
          </S.ContactForm>
        </S.ContactContainer>
      </div>
    </S.ContactSection>
  );
};

export default Contact;