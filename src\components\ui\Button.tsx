import React from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import { tokens } from '../../styles/tokens';

// Tipos para as variações do botão
type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  children: React.ReactNode;
}

// Estilos base do botão
const ButtonBase = styled(motion.button)<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${tokens.spacing[2]};
  font-family: ${tokens.typography.fontFamily.primary};
  font-weight: ${tokens.typography.fontWeight.semibold};
  border: none;
  border-radius: ${tokens.borderRadius.md};
  cursor: pointer;
  transition: all ${tokens.transitions.normal};
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
  
  ${({ fullWidth }) => fullWidth && css`
    width: 100%;
  `}
  
  &:disabled {
    opacity: ${tokens.effects.opacity[50]};
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &:focus-visible {
    outline: 2px solid ${tokens.colors.primary[500]};
    outline-offset: 2px;
  }
`;

// Variações de estilo
const buttonVariants = {
  primary: css`
    background-color: ${tokens.colors.primary[500]};
    color: ${tokens.colors.white};
    border: 1px solid ${tokens.colors.primary[500]};
    
    &:hover:not(:disabled) {
      background-color: ${tokens.colors.primary[600]};
      border-color: ${tokens.colors.primary[600]};
      transform: translateY(-2px);
      box-shadow: ${tokens.shadows.lg};
    }
    
    &:active {
      transform: translateY(0);
    }
  `,
  
  secondary: css`
    background-color: ${tokens.colors.gray[100]};
    color: ${tokens.colors.text.primary};
    border: 1px solid ${tokens.colors.gray[300]};
    
    &:hover:not(:disabled) {
      background-color: ${tokens.colors.gray[200]};
      border-color: ${tokens.colors.gray[400]};
      transform: translateY(-2px);
      box-shadow: ${tokens.shadows.md};
    }
    
    &:active {
      transform: translateY(0);
    }
  `,
  
  outline: css`
    background-color: transparent;
    color: ${tokens.colors.primary[500]};
    border: 1px solid ${tokens.colors.primary[500]};
    
    &:hover:not(:disabled) {
      background-color: ${tokens.colors.primary[500]};
      color: ${tokens.colors.white};
      transform: translateY(-2px);
      box-shadow: ${tokens.shadows.md};
    }
    
    &:active {
      transform: translateY(0);
    }
  `,
  
  ghost: css`
    background-color: transparent;
    color: ${tokens.colors.text.primary};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${tokens.colors.gray[100]};
      color: ${tokens.colors.primary[500]};
    }
    
    &:active {
      background-color: ${tokens.colors.gray[200]};
    }
  `
};

// Tamanhos do botão
const buttonSizes = {
  sm: css`
    padding: ${tokens.spacing[2]} ${tokens.spacing[3]};
    font-size: ${tokens.typography.fontSize.sm};
    min-height: 2rem;
  `,
  
  md: css`
    padding: ${tokens.spacing[3]} ${tokens.spacing[4]};
    font-size: ${tokens.typography.fontSize.base};
    min-height: 2.5rem;
  `,
  
  lg: css`
    padding: ${tokens.spacing[4]} ${tokens.spacing[6]};
    font-size: ${tokens.typography.fontSize.lg};
    min-height: 3rem;
  `
};

// Componente estilizado final
const StyledButton = styled(ButtonBase)`
  ${({ variant = 'primary' }) => buttonVariants[variant]}
  ${({ size = 'md' }) => buttonSizes[size]}
`;

// Spinner de loading
const LoadingSpinner = styled.div`
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

// Componente Button
export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  isLoading = false,
  leftIcon,
  rightIcon,
  children,
  disabled,
  ...props
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      disabled={disabled || isLoading}
      whileHover={{ scale: disabled || isLoading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || isLoading ? 1 : 0.98 }}
      {...props}
    >
      {isLoading && <LoadingSpinner />}
      {!isLoading && leftIcon && leftIcon}
      {children}
      {!isLoading && rightIcon && rightIcon}
    </StyledButton>
  );
};

export default Button;
