import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';
import { tokens } from '../../styles/tokens';

// Tipos para as variações do input
type InputSize = 'sm' | 'md' | 'lg';
type InputVariant = 'default' | 'filled' | 'flushed';

interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  size?: InputSize;
  variant?: InputVariant;
  error?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  label?: string;
  helperText?: string;
  errorText?: string;
}

interface TextAreaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'> {
  size?: InputSize;
  variant?: InputVariant;
  error?: boolean;
  label?: string;
  helperText?: string;
  errorText?: string;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

// Container do grupo de input
const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${tokens.spacing[2]};
  width: 100%;
`;

// Label do input
const InputLabel = styled.label<{ required?: boolean }>`
  font-size: ${tokens.typography.fontSize.sm};
  font-weight: ${tokens.typography.fontWeight.medium};
  color: ${tokens.colors.text.primary};

  ${({ required }) => required && css`
    &::after {
      content: ' *';
      color: ${tokens.colors.semantic.error};
    }
  `}
`;

// Container do input com ícones
const InputContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

// Estilos base do input
const InputBase = styled.input<InputProps>`
  width: 100%;
  font-family: ${tokens.typography.fontFamily.primary};
  border-radius: ${tokens.borderRadius.md};
  transition: all ${tokens.transitions.normal};
  outline: none;

  &::placeholder {
    color: ${tokens.colors.text.muted};
  }

  &:disabled {
    opacity: ${tokens.effects.opacity[50]};
    cursor: not-allowed;
    background-color: ${tokens.colors.gray[100]};
  }

  &.has-left-icon {
    padding-left: 2.5rem;
  }

  &.has-right-icon {
    padding-right: 2.5rem;
  }
`;

// Variações de estilo para Input
const inputVariants = {
  default: css<InputProps>`
    background-color: ${tokens.colors.background.primary};
    border: 1px solid ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.gray[300]};
    color: ${tokens.colors.text.primary};

    &:focus {
      border-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.primary[500]};
      box-shadow: 0 0 0 3px ${({ error }) =>
        error ? `${tokens.colors.semantic.error}20` : `${tokens.colors.primary[500]}20`};
    }

    &:hover:not(:disabled) {
      border-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.gray[400]};
    }
  `,

  filled: css<InputProps>`
    background-color: ${tokens.colors.gray[100]};
    border: 1px solid transparent;
    color: ${tokens.colors.text.primary};

    &:focus {
      background-color: ${tokens.colors.background.primary};
      border-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.primary[500]};
      box-shadow: 0 0 0 3px ${({ error }) =>
        error ? `${tokens.colors.semantic.error}20` : `${tokens.colors.primary[500]}20`};
    }

    &:hover:not(:disabled) {
      background-color: ${tokens.colors.gray[200]};
    }
  `,

  flushed: css<InputProps>`
    background-color: transparent;
    border: none;
    border-bottom: 2px solid ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.gray[300]};
    border-radius: 0;

    &:focus {
      border-bottom-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.primary[500]};
    }

    &:hover:not(:disabled) {
      border-bottom-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.gray[400]};
    }
  `
};

// Variações de estilo para TextArea
const textAreaVariants = {
  default: css<TextAreaProps>`
    background-color: ${tokens.colors.background.primary};
    border: 1px solid ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.gray[300]};
    color: ${tokens.colors.text.primary};

    &:focus {
      border-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.primary[500]};
      box-shadow: 0 0 0 3px ${({ error }) =>
        error ? `${tokens.colors.semantic.error}20` : `${tokens.colors.primary[500]}20`};
    }

    &:hover:not(:disabled) {
      border-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.gray[400]};
    }
  `,

  filled: css<TextAreaProps>`
    background-color: ${tokens.colors.gray[100]};
    border: 1px solid transparent;
    color: ${tokens.colors.text.primary};

    &:focus {
      background-color: ${tokens.colors.background.primary};
      border-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.primary[500]};
      box-shadow: 0 0 0 3px ${({ error }) =>
        error ? `${tokens.colors.semantic.error}20` : `${tokens.colors.primary[500]}20`};
    }

    &:hover:not(:disabled) {
      background-color: ${tokens.colors.gray[200]};
    }
  `,

  flushed: css<TextAreaProps>`
    background-color: transparent;
    border: none;
    border-bottom: 2px solid ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.gray[300]};
    border-radius: 0;

    &:focus {
      border-bottom-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.primary[500]};
    }

    &:hover:not(:disabled) {
      border-bottom-color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.gray[400]};
    }
  `
};

// Tamanhos do input
const inputSizes = {
  sm: css`
    padding: ${tokens.spacing[2]} ${tokens.spacing[3]};
    font-size: ${tokens.typography.fontSize.sm};
    min-height: 2rem;
  `,

  md: css`
    padding: ${tokens.spacing[3]} ${tokens.spacing[4]};
    font-size: ${tokens.typography.fontSize.base};
    min-height: 2.5rem;
  `,

  lg: css`
    padding: ${tokens.spacing[4]} ${tokens.spacing[4]};
    font-size: ${tokens.typography.fontSize.lg};
    min-height: 3rem;
  `
};

// Input estilizado final
const StyledInput = styled(InputBase)`
  ${({ variant = 'default' }) => inputVariants[variant]}
  ${({ size = 'md' }) => inputSizes[size]}
`;

// TextArea estilizado
const StyledTextArea = styled.textarea<TextAreaProps>`
  ${({ variant = 'default' }) => textAreaVariants[variant]}
  ${({ size = 'md' }) => inputSizes[size]}

  width: 100%;
  font-family: ${tokens.typography.fontFamily.primary};
  border-radius: ${tokens.borderRadius.md};
  transition: all ${tokens.transitions.normal};
  outline: none;
  min-height: 120px;
  resize: ${({ resize = 'vertical' }) => resize};

  &::placeholder {
    color: ${tokens.colors.text.muted};
  }

  &:disabled {
    opacity: ${tokens.effects.opacity[50]};
    cursor: not-allowed;
    background-color: ${tokens.colors.gray[100]};
  }
`;

// Ícones do input
const InputIcon = styled.div<{ position: 'left' | 'right' }>`
  position: absolute;
  ${({ position }) => position}: ${tokens.spacing[3]};
  color: ${tokens.colors.text.muted};
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
`;

// Texto de ajuda e erro
const HelperText = styled.p<{ error?: boolean }>`
  font-size: ${tokens.typography.fontSize.sm};
  color: ${({ error }) => error ? tokens.colors.semantic.error : tokens.colors.text.muted};
  margin: 0;
`;

// Componente Input principal
export const Input = forwardRef<HTMLInputElement, InputProps>(({
  size = 'md',
  variant = 'default',
  error = false,
  leftIcon,
  rightIcon,
  label,
  helperText,
  errorText,
  required,
  ...props
}, ref) => {
  const hasError = error || !!errorText;

  return (
    <InputGroup>
      {label && (
        <InputLabel required={required}>
          {label}
        </InputLabel>
      )}

      <InputContainer>
        {leftIcon && (
          <InputIcon position="left">
            {leftIcon}
          </InputIcon>
        )}

        <StyledInput
          ref={ref}
          size={size}
          variant={variant}
          error={hasError}
          className={`${leftIcon ? 'has-left-icon' : ''} ${rightIcon ? 'has-right-icon' : ''}`.trim()}
          {...props}
        />

        {rightIcon && (
          <InputIcon position="right">
            {rightIcon}
          </InputIcon>
        )}
      </InputContainer>

      {(helperText || errorText) && (
        <HelperText error={hasError}>
          {errorText || helperText}
        </HelperText>
      )}
    </InputGroup>
  );
});

// Componente TextArea
export const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({
  size = 'md',
  variant = 'default',
  error = false,
  label,
  helperText,
  errorText,
  required,
  resize = 'vertical',
  ...props
}, ref) => {
  const hasError = error || !!errorText;

  return (
    <InputGroup>
      {label && (
        <InputLabel required={required}>
          {label}
        </InputLabel>
      )}

      <StyledTextArea
        ref={ref}
        size={size}
        variant={variant}
        error={hasError}
        resize={resize}
        {...props}
      />

      {(helperText || errorText) && (
        <HelperText error={hasError}>
          {errorText || helperText}
        </HelperText>
      )}
    </InputGroup>
  );
});

Input.displayName = 'Input';
TextArea.displayName = 'TextArea';

export default Input;
