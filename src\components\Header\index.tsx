import React, { useState, useEffect } from 'react';
import { Link } from 'react-scroll';
import * as FaIcons from 'react-icons/fa';
import * as S from './styles';
import logo from '../../logo.png';

const Header: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [activeSection, setActiveSection] = useState('home');
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY);
      
      // Determinar a seção ativa com base na posição de rolagem
      const sections = ['home', 'services', 'projects'];
      const sectionElements = sections.map(id => document.getElementById(id));
      
      const currentSection = sectionElements.findIndex(element => {
        if (!element) return false;
        const rect = element.getBoundingClientRect();
        return rect.top <= 150 && rect.bottom >= 150;
      });

      if (currentSection !== -1) {
        setActiveSection(sections[currentSection]);
      }

      // Efeito de fade do header baseado na rolagem
      const headerElement = document.querySelector('header');
      if (headerElement) {
        const opacity = Math.min(window.scrollY / 300, 0.9);
        headerElement.style.backgroundColor = `rgba(255, 255, 255, ${opacity})`;
        
        // Ajusta a sombra conforme rola a página
        if (window.scrollY > 50) {
          headerElement.style.boxShadow = `0 4px 20px rgba(0, 0, 0, ${opacity * 0.1})`;
        } else {
          headerElement.style.boxShadow = 'none';
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    
    // Marcar como carregado após um pequeno atraso
    setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <S.HeaderContainer scrolled={scrollPosition > 50}>
      <div className="container">
        <S.HeaderContent>
          <S.Logo isLoaded={isLoaded}>
            <S.LogoImage src={logo} alt="Néri Consultoria" />
          </S.Logo>

          <S.MobileIcon onClick={toggleMenu}>
            {isOpen ? FaIcons.FaTimes({}) : FaIcons.FaBars({})}
          </S.MobileIcon>

          <S.Menu isOpen={isOpen}>
            <S.MenuItem 
              isLoaded={isLoaded}
              delayIndex={1}
              className={activeSection === 'home' ? 'active' : ''}
            >
              <Link 
                to="home" 
                spy={true} 
                smooth={true} 
                offset={-100} 
                duration={500}
                onClick={() => setIsOpen(false)}
              >
                Início
              </Link>
            </S.MenuItem>
            <S.MenuItem
              isLoaded={isLoaded}
              delayIndex={2}
              className={activeSection === 'services' ? 'active' : ''}
            >
              <Link
                to="services"
                spy={true}
                smooth={true}
                offset={-100}
                duration={500}
                onClick={() => setIsOpen(false)}
              >
                Serviços
              </Link>
            </S.MenuItem>
            <S.MenuItem
              isLoaded={isLoaded}
              delayIndex={3}
              className={activeSection === 'projects' ? 'active' : ''}
            >
              <Link
                to="projects"
                spy={true}
                smooth={true}
                offset={-100}
                duration={500}
                onClick={() => setIsOpen(false)}
              >
                Projetos
              </Link>
            </S.MenuItem>

          </S.Menu>
        </S.HeaderContent>
      </div>
    </S.HeaderContainer>
  );
};

export default Header; 