# 🎨 Sistema de Design - NeriConsult UI

Sistema de design unificado para garantir consistência visual e facilitar o desenvolvimento.

## 📋 **Componentes Disponíveis**

### 🔘 **Button**
Botão padronizado com múltiplas variações e estados.

```tsx
import { Button } from '../ui';

// Variações
<Button variant="primary">Primário</Button>
<Button variant="secondary">Secundário</Button>
<Button variant="outline">Contorno</Button>
<Button variant="ghost">Fantasma</Button>

// Tamanhos
<Button size="sm">Pequeno</Button>
<Button size="md">Médio</Button>
<Button size="lg">Grande</Button>

// Estados
<Button isLoading>Carregando...</Button>
<Button disabled>Desabilitado</Button>

// Com ícones
<Button leftIcon={<FaIcon />}>Com ícone</Button>
<Button rightIcon={<FaIcon />}>Com ícone</Button>
```

### 🃏 **Card**
Container padronizado para conteúdo.

```tsx
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '../ui';

<Card variant="default" padding="md" hoverable>
  <CardHeader>
    <CardTitle>Título do Card</CardTitle>
  </CardHeader>
  <CardContent>
    <p>Conteúdo do card...</p>
  </CardContent>
  <CardFooter>
    <Button>Ação</Button>
  </CardFooter>
</Card>

// Cards especializados
<ServiceCard hoverable>Conteúdo do serviço</ServiceCard>
<TestimonialCard>Depoimento</TestimonialCard>
```

### 📝 **Input & TextArea**
Campos de formulário padronizados.

```tsx
import { Input, TextArea } from '../ui';

<Input
  label="Nome"
  placeholder="Digite seu nome"
  required
  error={hasError}
  errorText="Campo obrigatório"
  leftIcon={<FaUser />}
/>

<TextArea
  label="Mensagem"
  placeholder="Digite sua mensagem"
  rows={5}
  resize="vertical"
/>
```

### 🔤 **Typography**
Sistema tipográfico consistente.

```tsx
import { H1, H2, SectionTitle, Text, Lead } from '../ui';

<H1 align="center" color="primary">Título Principal</H1>
<H2 color="secondary">Subtítulo</H2>
<SectionTitle align="center">Título de Seção</SectionTitle>
<Lead>Texto de destaque</Lead>
<Text size="lg" color="muted">Texto normal</Text>
```

## 🎨 **Design Tokens**

### Cores
```tsx
import { tokens } from '../ui';

// Cores primárias
tokens.colors.primary[500]  // #000000
tokens.colors.primary[600]  // #1a1a1a

// Cores semânticas
tokens.colors.semantic.success  // #10b981
tokens.colors.semantic.error    // #ef4444

// Cores de texto
tokens.colors.text.primary    // #000000
tokens.colors.text.secondary  // #555555
```

### Espaçamentos
```tsx
// Sistema de espaçamento (rem)
tokens.spacing[4]   // 1rem (16px)
tokens.spacing[8]   // 2rem (32px)
tokens.spacing[16]  // 4rem (64px)
```

### Tipografia
```tsx
// Tamanhos de fonte
tokens.typography.fontSize.base  // 1rem
tokens.typography.fontSize.lg    // 1.125rem
tokens.typography.fontSize['2xl'] // 1.5rem

// Pesos de fonte
tokens.typography.fontWeight.normal   // 400
tokens.typography.fontWeight.semibold // 600
```

## 📱 **Responsividade**

### Breakpoints
```tsx
tokens.breakpoints.sm   // 640px
tokens.breakpoints.md   // 768px
tokens.breakpoints.lg   // 1024px
tokens.breakpoints.xl   // 1280px
```

### Uso em Styled Components
```tsx
import styled from 'styled-components';
import { tokens } from '../ui';

const ResponsiveContainer = styled.div`
  padding: ${tokens.spacing[8]};
  
  @media (max-width: ${tokens.breakpoints.md}) {
    padding: ${tokens.spacing[4]};
  }
`;
```

## 🎭 **Animações e Transições**

```tsx
// Durações padronizadas
tokens.transitions.fast    // 150ms ease
tokens.transitions.normal  // 300ms ease
tokens.transitions.slow    // 500ms ease

// Sombras
tokens.shadows.sm   // Sombra pequena
tokens.shadows.md   // Sombra média
tokens.shadows.lg   // Sombra grande
```

## 🔧 **Customização**

### Tema Personalizado
```tsx
import { ThemeProvider } from 'styled-components';
import { theme } from '../ui';

const customTheme = {
  ...theme,
  colors: {
    ...theme.colors,
    primary: '#custom-color'
  }
};

<ThemeProvider theme={customTheme}>
  <App />
</ThemeProvider>
```

## 📚 **Exemplos de Uso**

### Formulário Completo
```tsx
import { Card, Input, TextArea, Button, H3 } from '../ui';

<Card padding="lg">
  <H3>Contato</H3>
  <Input label="Nome" required />
  <Input label="Email" type="email" required />
  <TextArea label="Mensagem" required />
  <Button variant="primary" fullWidth>
    Enviar
  </Button>
</Card>
```

### Seção de Serviços
```tsx
import { SectionTitle, SectionSubtitle, ServiceCard, Text } from '../ui';

<section>
  <SectionTitle align="center">Nossos Serviços</SectionTitle>
  <SectionSubtitle align="center">
    Soluções completas para seu negócio
  </SectionSubtitle>
  
  <div style={{ display: 'grid', gap: '2rem' }}>
    <ServiceCard hoverable>
      <Text size="xl" align="center">Desenvolvimento</Text>
      <Text align="center" color="secondary">
        Soluções personalizadas
      </Text>
    </ServiceCard>
  </div>
</section>
```

## ✅ **Boas Práticas**

1. **Use sempre os tokens** em vez de valores hardcoded
2. **Prefira componentes UI** aos styled-components customizados
3. **Mantenha consistência** nas variações de componentes
4. **Teste responsividade** em todos os breakpoints
5. **Documente customizações** específicas

## 🚀 **Benefícios**

- ✅ **Consistência visual** garantida
- ✅ **Desenvolvimento mais rápido**
- ✅ **Manutenção simplificada**
- ✅ **Reutilização de código**
- ✅ **Acessibilidade melhorada**
- ✅ **Design escalável**
