import styled, { css } from 'styled-components';
import { tokens } from '../../styles/tokens';

// Tipos para variações de texto
type TextAlign = 'left' | 'center' | 'right' | 'justify';
type TextColor = 'primary' | 'secondary' | 'muted' | 'inverse' | 'success' | 'warning' | 'error' | 'info';

interface TypographyProps {
  align?: TextAlign;
  color?: TextColor;
  className?: string;
}

// Mapeamento de cores
const textColors = {
  primary: tokens.colors.text.primary,
  secondary: tokens.colors.text.secondary,
  muted: tokens.colors.text.muted,
  inverse: tokens.colors.text.inverse,
  success: tokens.colors.semantic.success,
  warning: tokens.colors.semantic.warning,
  error: tokens.colors.semantic.error,
  info: tokens.colors.semantic.info
};

// Estilos base para tipografia
const baseTypographyStyles = css<TypographyProps>`
  font-family: ${tokens.typography.fontFamily.primary};
  color: ${({ color = 'primary' }) => textColors[color]};
  text-align: ${({ align = 'left' }) => align};
  margin: 0;
  line-height: ${tokens.typography.lineHeight.normal};
`;

// Headings
export const H1 = styled.h1<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize['6xl']};
  font-weight: ${tokens.typography.fontWeight.bold};
  line-height: ${tokens.typography.lineHeight.tight};
  letter-spacing: ${tokens.typography.letterSpacing.tight};
  
  @media (max-width: ${tokens.breakpoints.md}) {
    font-size: ${tokens.typography.fontSize['4xl']};
  }
  
  @media (max-width: ${tokens.breakpoints.sm}) {
    font-size: ${tokens.typography.fontSize['3xl']};
  }
`;

export const H2 = styled.h2<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize['5xl']};
  font-weight: ${tokens.typography.fontWeight.bold};
  line-height: ${tokens.typography.lineHeight.tight};
  letter-spacing: ${tokens.typography.letterSpacing.tight};
  
  @media (max-width: ${tokens.breakpoints.md}) {
    font-size: ${tokens.typography.fontSize['3xl']};
  }
  
  @media (max-width: ${tokens.breakpoints.sm}) {
    font-size: ${tokens.typography.fontSize['2xl']};
  }
`;

export const H3 = styled.h3<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize['4xl']};
  font-weight: ${tokens.typography.fontWeight.bold};
  line-height: ${tokens.typography.lineHeight.tight};
  
  @media (max-width: ${tokens.breakpoints.md}) {
    font-size: ${tokens.typography.fontSize['2xl']};
  }
  
  @media (max-width: ${tokens.breakpoints.sm}) {
    font-size: ${tokens.typography.fontSize.xl};
  }
`;

export const H4 = styled.h4<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize['3xl']};
  font-weight: ${tokens.typography.fontWeight.semibold};
  line-height: ${tokens.typography.lineHeight.tight};
  
  @media (max-width: ${tokens.breakpoints.md}) {
    font-size: ${tokens.typography.fontSize.xl};
  }
  
  @media (max-width: ${tokens.breakpoints.sm}) {
    font-size: ${tokens.typography.fontSize.lg};
  }
`;

export const H5 = styled.h5<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize['2xl']};
  font-weight: ${tokens.typography.fontWeight.semibold};
  line-height: ${tokens.typography.lineHeight.snug};
  
  @media (max-width: ${tokens.breakpoints.md}) {
    font-size: ${tokens.typography.fontSize.lg};
  }
`;

export const H6 = styled.h6<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize.xl};
  font-weight: ${tokens.typography.fontWeight.semibold};
  line-height: ${tokens.typography.lineHeight.snug};
  
  @media (max-width: ${tokens.breakpoints.md}) {
    font-size: ${tokens.typography.fontSize.base};
  }
`;

// Parágrafos e texto
export const Text = styled.p<TypographyProps & { size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' }>`
  ${baseTypographyStyles}
  font-size: ${({ size = 'base' }) => tokens.typography.fontSize[size]};
  line-height: ${tokens.typography.lineHeight.relaxed};
`;

export const Lead = styled.p<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize.xl};
  font-weight: ${tokens.typography.fontWeight.normal};
  line-height: ${tokens.typography.lineHeight.relaxed};
  
  @media (max-width: ${tokens.breakpoints.md}) {
    font-size: ${tokens.typography.fontSize.lg};
  }
`;

export const Small = styled.small<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize.sm};
  line-height: ${tokens.typography.lineHeight.normal};
`;

export const Caption = styled.span<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize.xs};
  line-height: ${tokens.typography.lineHeight.normal};
  color: ${({ color = 'muted' }) => textColors[color]};
`;

// Texto com estilos especiais
export const Strong = styled.strong<TypographyProps>`
  ${baseTypographyStyles}
  font-weight: ${tokens.typography.fontWeight.bold};
`;

export const Emphasis = styled.em<TypographyProps>`
  ${baseTypographyStyles}
  font-style: italic;
`;

export const Code = styled.code<TypographyProps>`
  font-family: ${tokens.typography.fontFamily.mono};
  font-size: 0.875em;
  color: ${({ color = 'primary' }) => textColors[color]};
  background-color: ${tokens.colors.gray[100]};
  padding: ${tokens.spacing[1]} ${tokens.spacing[2]};
  border-radius: ${tokens.borderRadius.sm};
`;

// Componentes especializados para seções
export const SectionTitle = styled(H2)<TypographyProps>`
  margin-bottom: ${tokens.spacing[4]};
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -${tokens.spacing[2]};
    left: ${({ align }) => align === 'center' ? '50%' : '0'};
    transform: ${({ align }) => align === 'center' ? 'translateX(-50%)' : 'none'};
    width: 60px;
    height: 3px;
    background-color: ${tokens.colors.primary[500]};
    border-radius: ${tokens.borderRadius.full};
  }
`;

export const SectionSubtitle = styled(Text)<TypographyProps>`
  font-size: ${tokens.typography.fontSize.lg};
  color: ${({ color = 'secondary' }) => textColors[color]};
  margin-bottom: ${tokens.spacing[8]};
  max-width: 600px;
  margin-left: ${({ align }) => align === 'center' ? 'auto' : '0'};
  margin-right: ${({ align }) => align === 'center' ? 'auto' : '0'};
`;

// Lista estilizada
export const List = styled.ul<TypographyProps>`
  ${baseTypographyStyles}
  padding-left: ${tokens.spacing[6]};
  
  li {
    margin-bottom: ${tokens.spacing[2]};
    line-height: ${tokens.typography.lineHeight.relaxed};
    
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

export const OrderedList = styled.ol<TypographyProps>`
  ${baseTypographyStyles}
  padding-left: ${tokens.spacing[6]};
  
  li {
    margin-bottom: ${tokens.spacing[2]};
    line-height: ${tokens.typography.lineHeight.relaxed};
    
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

// Link estilizado
export const Link = styled.a<TypographyProps>`
  ${baseTypographyStyles}
  color: ${tokens.colors.primary[500]};
  text-decoration: none;
  transition: all ${tokens.transitions.fast};
  
  &:hover {
    color: ${tokens.colors.primary[600]};
    text-decoration: underline;
  }
  
  &:focus-visible {
    outline: 2px solid ${tokens.colors.primary[500]};
    outline-offset: 2px;
    border-radius: ${tokens.borderRadius.sm};
  }
`;

// Blockquote
export const Blockquote = styled.blockquote<TypographyProps>`
  ${baseTypographyStyles}
  font-size: ${tokens.typography.fontSize.lg};
  font-style: italic;
  border-left: 4px solid ${tokens.colors.primary[500]};
  padding-left: ${tokens.spacing[6]};
  margin: ${tokens.spacing[6]} 0;
  color: ${({ color = 'secondary' }) => textColors[color]};
`;

export default {
  H1,
  H2,
  H3,
  H4,
  H5,
  H6,
  Text,
  Lead,
  Small,
  Caption,
  Strong,
  Emphasis,
  Code,
  SectionTitle,
  SectionSubtitle,
  List,
  OrderedList,
  Link,
  Blockquote
};
