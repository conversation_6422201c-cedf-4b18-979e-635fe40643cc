import styled from 'styled-components';

export const ContactSection = styled.section`
  background-color: var(--background-color);
  padding: 6rem 0;
  color: var(--text-color);
`;

export const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: var(--text-color);

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

export const SectionSubtitle = styled.p`
  font-size: 1.25rem;
  text-align: center;
  max-width: 600px;
  margin: 0 auto 4rem;
  color: var(--light-text-color);

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: 3rem;
  }
`;

export const ContactContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 2rem;
  max-width: 1100px;
  margin: 0 auto;
  
  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
`;

export const ContactInfo = styled.div`
  background: linear-gradient(135deg, var(--primary-color), #333333);
  border-radius: 10px;
  padding: 3rem 2rem;
  color: white;
  height: 100%;
  box-shadow: var(--box-shadow);
`;

export const ContactTitle = styled.h3`
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
`;

export const ContactText = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  color: white;
`;

export const ContactInfoList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

export const ContactInfoItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

export const ContactInfoIcon = styled.div`
  background-color: rgba(255, 255, 255, 0.2);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
`;

export const ContactInfoLabel = styled.p`
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  opacity: 0.8;
  color: white;
`;

export const ContactInfoValue = styled.p`
  font-size: 1rem;
  font-weight: 500;
  color: white;
`;

export const ContactForm = styled.form`
  background-color: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--gray-300);
  border-radius: 10px;
  padding: 3rem 2rem;
  box-shadow: var(--box-shadow);
  
  @media (max-width: 768px) {
    padding: 2rem 1.5rem;
  }
`;

interface InputProps {
  error?: boolean;
}

export const InputGroup = styled.div`
  margin-bottom: 1.5rem;
  width: 100%;
`;

export const InputRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  
  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

export const FormLabel = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
`;

export const Input = styled.input<InputProps>`
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid ${({ error }) => error ? 'var(--red, #dc2626)' : 'var(--gray-300)'};
  border-radius: 6px;
  font-size: 1rem;
  color: var(--text-color);
  background-color: ${({ error }) => error ? 'rgba(220, 38, 38, 0.05)' : 'var(--background-color)'};
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

export const Select = styled.select`
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  font-size: 1rem;
  color: var(--text-color);
  background-color: var(--background-color);
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

export const TextArea = styled.textarea<InputProps>`
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid ${({ error }) => error ? 'var(--red, #dc2626)' : 'var(--gray-300)'};
  border-radius: 6px;
  font-size: 1rem;
  color: var(--text-color);
  resize: vertical;
  min-height: 120px;
  background-color: ${({ error }) => error ? 'rgba(220, 38, 38, 0.05)' : 'var(--background-color)'};
  font-family: inherit;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

export const ErrorText = styled.span`
  color: var(--red, #dc2626);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: block;
`;

export const SubmitButton = styled.button`
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 6px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  
  &:hover:not(:disabled) {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(37, 99, 235, 0.2);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

export const SuccessMessage = styled.div`
  text-align: center;
  padding: 2rem;
  
  svg {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
  }
  
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
  }
  
  p {
    color: var(--light-text-color);
    font-size: 1.125rem;
  }
`;