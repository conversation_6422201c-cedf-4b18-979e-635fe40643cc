import styled from 'styled-components';

interface FooterLogoProps {
  isLoaded: boolean;
}

interface FooterColumnProps {
  isLoaded: boolean;
  delayIndex: number;
}

interface FooterBottomProps {
  isLoaded: boolean;
  delayIndex: number;
}

export const CircuitCanvas = styled.canvas`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.2;
  pointer-events: none;
`;

export const FooterContainer = styled.footer`
  background-color: var(--black);
  color: var(--white);
  padding: 5rem 0 2rem;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.85));
    backdrop-filter: blur(5px);
    z-index: -1;
  }
  
  &.visible::after {
    opacity: 1;
  }
  
  /* Efeito de brilho nas bordas quando visível */
  &.visible {
    &::before {
      animation: glowPulse 3s infinite alternate;
    }
  }
  
  @keyframes glowPulse {
    0% {
      box-shadow: inset 0 0 30px rgba(0, 128, 255, 0.1);
    }
    50% {
      box-shadow: inset 0 0 40px rgba(0, 255, 128, 0.15);
    }
    100% {
      box-shadow: inset 0 0 30px rgba(255, 0, 128, 0.1);
    }
  }
`;

export const FooterContent = styled.div`
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
`;

export const FooterLogo = styled.div<FooterLogoProps>`
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2.5rem;
  letter-spacing: -0.5px;
  color: var(--white);
  display: flex;
  align-items: center;
  opacity: ${({ isLoaded }) => isLoaded ? 1 : 0};
  transform: ${({ isLoaded }) => isLoaded ? 'translateY(0)' : 'translateY(20px)'};
  transition: opacity 0.5s ease, transform 0.5s ease;
  justify-content: center;
  width: 100%;
  
  span {
    color: var(--accent-color);
  }
`;

export const LogoImage = styled.img`
  height: 120px;
  width: auto;
  filter: brightness(0) invert(1);
  
  @media (max-width: 768px) {
    height: 50px;
  }
`;

export const FooterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2.5rem;
  position: relative;
  z-index: 1;
`;

export const FooterColumn = styled.div<FooterColumnProps>`
  opacity: ${({ isLoaded }) => isLoaded ? 1 : 0};
  transform: ${({ isLoaded }) => isLoaded ? 'translateY(0)' : 'translateY(20px)'};
  transition: ${({ delayIndex }) => `opacity 0.5s ease ${delayIndex * 0.1}s, transform 0.5s ease ${delayIndex * 0.1}s`};
`;

export const FooterTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--white);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
    transform: scaleX(1);
    transition: transform 0.3s ease;
  }
  
  /* Efeito de luz ao passar o mouse */
  &:hover::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 40px;
    height: 2px;
    box-shadow: 0 0 10px 2px var(--primary-color);
    opacity: 0.7;
    animation: pulseLight 1.5s infinite;
  }
  
  @keyframes pulseLight {
    0% { opacity: 0.3; }
    50% { opacity: 0.7; }
    100% { opacity: 0.3; }
  }
`;

export const FooterText = styled.p`
  font-size: 0.9rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
`;

export const FooterLinks = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

export const FooterLink = styled.a`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  position: relative;
  
  &:hover {
    color: var(--primary-color);
    transform: translateX(5px);
    text-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.5);
  }
  
  /* Indicador de seta com transição suave */
  &::before {
    content: '›';
    margin-right: 0.5rem;
    color: var(--primary-color);
    opacity: 0;
    transition: all 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  /* Efeito de sublinhado animado como no header */
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
  }
  
  &:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }
`;

export const ContactInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

export const ContactItem = styled.p`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  
  &:hover {
    color: var(--primary-color);
    transform: translateX(5px);
  }
  
  strong {
    color: var(--white);
    margin-right: 0.5rem;
  }
`;

export const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
`;

export const SocialLink = styled.a`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  /* Efeito de pulso ao passar o mouse */
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: transparent;
    border: 1px solid transparent;
    transition: all 0.3s ease;
  }
  
  &:hover::after {
    animation: pulseRing 1.5s infinite;
    border-color: var(--primary-color);
  }
  
  @keyframes pulseRing {
    0% {
      transform: scale(1);
      opacity: 0.6;
    }
    50% {
      transform: scale(1.2);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 0;
    }
  }
`;

export const FooterBottom = styled.div<FooterBottomProps>`
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  opacity: ${({ isLoaded }) => isLoaded ? 1 : 0};
  transform: ${({ isLoaded }) => isLoaded ? 'translateY(0)' : 'translateY(20px)'};
  transition: ${({ delayIndex }) => `opacity 0.5s ease ${delayIndex * 0.1}s, transform 0.5s ease ${delayIndex * 0.1}s`};
  position: relative;
  z-index: 1;
  
  /* Efeito de linha digital */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 128, 255, 0.4),
      rgba(0, 255, 128, 0.4),
      rgba(255, 0, 128, 0.4),
      transparent
    );
    background-size: 200% 100%;
    animation: gradientSlide 4s linear infinite;
  }
  
  @keyframes gradientSlide {
    0% { background-position: 0% 0; }
    100% { background-position: 200% 0; }
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
`;

export const Copyright = styled.p`
  font-size: 0.9rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  width: 100%;
  margin: 0 auto;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    color: var(--primary-color);
  }
`;

export const FooterBottomLinks = styled.div`
  display: flex;
  gap: 1.5rem;
`;

export const FooterBottomLink = styled.a`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.3s ease;
  
  &:hover {
    color: var(--primary-color);
  }
  
  /* Efeito de sublinhado animado */
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
  }
  
  &:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }
`;