{"name": "neri-consult", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "^4.4.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/aos": "^3.0.7", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-scroll": "^1.8.10", "@types/styled-components": "^5.1.34", "aos": "^2.3.4", "framer-motion": "^12.7.3", "gsap": "^3.12.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-scripts": "5.0.1", "react-scroll": "^1.9.3", "react-tsparticles": "^2.12.2", "styled-components": "^6.1.17", "tsparticles": "^2.12.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}