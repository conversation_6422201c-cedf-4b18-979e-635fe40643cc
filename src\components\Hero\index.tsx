import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-scroll';
import { motion, useAnimation } from 'framer-motion';
import { FaArrowRight, FaChevronDown, FaCode } from 'react-icons/fa';
import * as S from './styles';

const MatrixEffect: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Definir tamanho do canvas
    const setCanvasSize = () => {
      if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
      }
    };
    
    setCanvasSize();
    window.addEventListener('resize', setCanvasSize);
    
    // Tamanho da fonte e caracteres
    ctx.font = '10px monospace';
    const characters = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    
    // Colunas
    const columns = Math.floor(canvas.width / 10);
    const drops: number[] = [];
    
    // Inicializar posição das gotas
    for (let i = 0; i < columns; i++) {
      drops[i] = Math.random() * -100;
    }
    
    // Função para desenhar
    const draw = () => {
      if (!ctx || !canvas) return;
      
      // Fundo semi-transparente para criar efeito de desvanecimento
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Texto verde
      ctx.fillStyle = 'rgba(0, 255, 120, 0.2)';
      
      // Loop através de cada coluna
      for (let i = 0; i < drops.length; i++) {
        // Caractere aleatório
        const text = characters.charAt(Math.floor(Math.random() * characters.length));
        
        // Posição x = i*tamanho da fonte, y = valor da posição da gota
        ctx.fillText(text, i * 10, drops[i] * 10);
        
        // Incrementar posição Y em 1 unidade
        drops[i]++;
        
        // Enviar a gota de volta ao topo após sair da tela
        // Adicionando randomização para espalhamento
        if (drops[i] * 10 > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }
      }
    };
    
    // Loop de animação
    const interval = setInterval(draw, 35);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('resize', setCanvasSize);
    };
  }, []);
  
  return (
    <canvas 
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
        opacity: 0.5,
        pointerEvents: 'none'
      }}
    />
  );
};

const TechParticles: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Definir largura e altura do canvas
    const setCanvasSize = () => {
      if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
      }
    };
    
    // Chamar setCanvasSize inicialmente e em caso de redimensionamento
    setCanvasSize();
    window.addEventListener('resize', setCanvasSize);
    
    // Configuração do mouse
    let mouse = {
      x: null as number | null,
      y: null as number | null,
      radius: 150
    };
    
    // Evento para capturar posição do mouse
    window.addEventListener('mousemove', (event) => {
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        mouse.x = event.clientX - rect.left;
        mouse.y = event.clientY - rect.top;
      }
    });
    
    // Remover interação do mouse quando sair da área
    window.addEventListener('mouseout', () => {
      mouse.x = null;
      mouse.y = null;
    });
    
    // Criar partículas
    interface IParticle {
      x: number;
      y: number;
      size: number;
      baseX: number;
      baseY: number;
      density: number;
      speedX: number;
      speedY: number;
      color: string;
      update: () => void;
      draw: () => void;
    }
    
    const particlesArray: IParticle[] = [];
    const particleCount = 100;
    
    class Particle implements IParticle {
      x: number;
      y: number;
      size: number;
      baseX: number;
      baseY: number;
      density: number;
      speedX: number;
      speedY: number;
      color: string;
      
      constructor() {
        this.x = Math.random() * (canvas?.width || window.innerWidth);
        this.y = Math.random() * (canvas?.height || window.innerHeight);
        this.baseX = this.x;
        this.baseY = this.y;
        this.size = Math.random() * 2 + 0.5;
        this.density = Math.random() * 30 + 1;
        this.speedX = Math.random() * 0.5 - 0.25;
        this.speedY = Math.random() * 0.5 - 0.25;
        this.color = `rgba(255, 255, 255, ${Math.random() * 0.2 + 0.1})`;
      }
      
      update() {
        // Movimento normal das partículas
        this.x += this.speedX;
        this.y += this.speedY;
        
        // Interação com o mouse
        if (mouse.x !== null && mouse.y !== null) {
          const dx = mouse.x - this.x;
          const dy = mouse.y - this.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < mouse.radius) {
            // Direção da força exercida pelo mouse
            const forceDirectionX = dx / distance;
            const forceDirectionY = dy / distance;
            
            // Distância do mouse inversa (quanto mais perto, mais força)
            const maxDistance = mouse.radius;
            const force = (maxDistance - distance) / maxDistance;
            
            // Movimento em direção ao mouse
            const directionX = forceDirectionX * force * this.density;
            const directionY = forceDirectionY * force * this.density;
            
            this.x += directionX;
            this.y += directionY;
          }
        }
        
        // Retornar gradualmente à posição original quando não há interação
        const dx = this.baseX - this.x;
        const dy = this.baseY - this.y;
        this.x += dx * 0.02;
        this.y += dy * 0.02;
        
        // Voltar ao canvas quando sair dos limites
        if (this.x < 0 || this.x > (canvas?.width || window.innerWidth)) {
          this.x = Math.random() * (canvas?.width || window.innerWidth);
          this.baseX = this.x;
        }
        if (this.y < 0 || this.y > (canvas?.height || window.innerHeight)) {
          this.y = Math.random() * (canvas?.height || window.innerHeight);
          this.baseY = this.y;
        }
      }
      
      draw() {
        if (ctx) {
          ctx.fillStyle = this.color;
          ctx.beginPath();
          ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
          ctx.fill();
        }
      }
    }
    
    // Inicializar partículas
    const init = () => {
      for (let i = 0; i < particleCount; i++) {
        particlesArray.push(new Particle());
      }
    };
    
    init();
    
    // Conectar partículas próximas com linhas
    const connect = () => {
      if (!ctx) return;
      
      const maxDistance = 150;
      for (let a = 0; a < particlesArray.length; a++) {
        for (let b = a; b < particlesArray.length; b++) {
          const dx = particlesArray[a].x - particlesArray[b].x;
          const dy = particlesArray[a].y - particlesArray[b].y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < maxDistance) {
            const opacity = 1 - distance / maxDistance;
            ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.15})`;
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(particlesArray[a].x, particlesArray[a].y);
            ctx.lineTo(particlesArray[b].x, particlesArray[b].y);
            ctx.stroke();
          }
        }
      }
    };
    
    // Animar partículas
    const animate = () => {
      if (!ctx || !canvas) return;
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      for (let i = 0; i < particlesArray.length; i++) {
        particlesArray[i].update();
        particlesArray[i].draw();
      }
      
      connect();
      requestAnimationFrame(animate);
    };
    
    animate();
    
    // Limpar
    return () => {
      window.removeEventListener('resize', setCanvasSize);
      window.removeEventListener('mousemove', () => {});
      window.removeEventListener('mouseout', () => {});
    };
  }, []);
  
  return (
    <canvas 
      ref={canvasRef} 
      style={{ 
        position: 'absolute', 
        top: 0, 
        left: 0, 
        width: '100%', 
        height: '100%', 
        zIndex: 2, 
        pointerEvents: 'none' 
      }} 
    />
  );
};

const GlitchText: React.FC<{ text: string; delay?: number }> = ({ text, delay = 0 }) => {
  const [displayText, setDisplayText] = useState(text);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    // Aguardar o delay inicial
    const startTimeout = setTimeout(() => {
      // Iniciar o efeito de glitch com intervalos aleatórios
      const startGlitchEffect = () => {
        // Limpar qualquer intervalo existente
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
        
        // Função para criar caracteres aleatórios
        const createGlitchText = () => {
          const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_-+={}[]|\\:;"\'<>,.?/';
          let result = '';
          const positions = Math.floor(Math.random() * 3) + 1; // 1 a 3 posições para glitch
          
          // Copia o texto original
          result = text;
          
          // Substitui caracteres aleatórios
          for (let i = 0; i < positions; i++) {
            const pos = Math.floor(Math.random() * text.length);
            const randomChar = chars.charAt(Math.floor(Math.random() * chars.length));
            result = result.substring(0, pos) + randomChar + result.substring(pos + 1);
          }
          
          return result;
        };
        
        // Iniciar o loop de efeito glitch
        let isGlitching = false;
        
        intervalRef.current = setInterval(() => {
          // Determina aleatoriamente se deve glitch ou não
          if (Math.random() < 0.1 && !isGlitching) {
            isGlitching = true;
            
            // Sequência rápida de glitch
            const glitchInterval = setInterval(() => {
              setDisplayText(createGlitchText());
            }, 50);
            
            // Parar o glitch após um período
            setTimeout(() => {
              clearInterval(glitchInterval);
              setDisplayText(text);
              isGlitching = false;
            }, Math.random() * 400 + 100);
          }
        }, 2000); // Verifica a cada 2 segundos
      };
      
      startGlitchEffect();
    }, delay);
    
    return () => {
      if (startTimeout) clearTimeout(startTimeout);
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [text, delay]);
  
  return <span style={{ fontFamily: 'inherit' }}>{displayText}</span>;
};

// Novo componente de efeito de terminal/console
const TerminalEffect: React.FC = () => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    if (!terminalRef.current) return;
    
    const commands = [
      'npm install neri-consultoria',
      'yarn add tech-solutions',
      'git commit -m "Implementando soluções inovadoras"',
      'docker build -t neri/tech-service .',
      'kubectl deploy neri-apps',
      'aws deploy --region=global',
      './start-innovation.sh'
    ];
    
    let commandIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    let typingSpeed = 100;
    
    const type = () => {
      const currentCommand = commands[commandIndex];
      
      if (isDeleting) {
        if (terminalRef.current) {
          terminalRef.current.innerText = currentCommand.substring(0, charIndex - 1);
          charIndex--;
        }
        
        typingSpeed = 50;
        
        if (charIndex === 0) {
          isDeleting = false;
          commandIndex = (commandIndex + 1) % commands.length;
          typingSpeed = 500; // Pausa entre comandos
        }
      } else {
        if (terminalRef.current) {
          terminalRef.current.innerText = currentCommand.substring(0, charIndex + 1);
          charIndex++;
        }
        
        typingSpeed = 100;
        
        if (charIndex === currentCommand.length) {
          isDeleting = true;
          typingSpeed = 1500; // Pausa antes de começar a deletar
        }
      }
      
      timeoutRef.current = setTimeout(type, typingSpeed);
    };
    
    // Iniciar animação
    timeoutRef.current = setTimeout(type, 1000);
    
    // Limpar timeout em caso de desmontagem
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return (
    <S.TerminalContainer>
      <S.TerminalHeader>
        <S.TerminalDot color="#ff5f57" />
        <S.TerminalDot color="#febc2e" />
        <S.TerminalDot color="#28c840" />
        <S.TerminalTitle>terminal@neri-tech</S.TerminalTitle>
      </S.TerminalHeader>
      <S.TerminalBody>
        <span style={{ color: '#4ade80' }}>$ </span>
        <span ref={terminalRef}></span>
        <span className="cursor">|</span>
      </S.TerminalBody>
    </S.TerminalContainer>
  );
};

// Componente de código digital
const CodeEffect: React.FC = () => {
  const [randomCode, setRandomCode] = useState<string[]>([]);
  
  useEffect(() => {
    const codeSnippets = [
      'function optimizeSolution(data) {',
      '  const result = data.map(item => {',
      '    return processItem(item);',
      '  });',
      '  return result.filter(Boolean);',
      '}',
      '',
      'class TechSolution {',
      '  constructor(client) {',
      '    this.client = client;',
      '    this.innovations = [];',
      '  }',
      '',
      '  implement(technology) {',
      '    return this.client.success();',
      '  }',
      '}'
    ];
    
    setRandomCode(codeSnippets);
    
    const interval = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * codeSnippets.length);
      const newCode = [...codeSnippets];
      newCode[randomIndex] = newCode[randomIndex].split('').map(char => 
        Math.random() > 0.8 ? String.fromCharCode(Math.floor(Math.random() * 94) + 33) : char
      ).join('');
      
      setRandomCode(newCode);
      
      // Restaurar código original após glitch
      setTimeout(() => {
        setRandomCode(codeSnippets);
      }, 200);
    }, 3000);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <S.CodeContainer>
      <S.CodeHeader>
        <>{FaCode({ style: { marginRight: '8px' } })}</>
        neri-solution.js
      </S.CodeHeader>
      <S.CodeBody>
        {randomCode.map((line, index) => (
          <div key={index} style={{ 
            color: line.includes('function') || line.includes('class') ? '#4ade80' : 
                  line.includes('return') ? '#f87171' : 
                  line.includes('constructor') ? '#60a5fa' : 
                  '#e2e8f0',
            opacity: line ? 1 : 0.5
          }}>
            {line || ' '}
          </div>
        ))}
      </S.CodeBody>
    </S.CodeContainer>
  );
};

// Componente simplificado de efeito para dispositivos móveis
const MobileBackgroundEffect: React.FC = () => {
  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      zIndex: 1,
      opacity: 0.4,
      backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(60, 60, 60, 0.4) 1px, transparent 1px), radial-gradient(circle at 75% 75%, rgba(60, 60, 60, 0.4) 1px, transparent 1px)',
      backgroundSize: '50px 50px',
      pointerEvents: 'none'
    }} />
  );
};

const Hero: React.FC = () => {
  const controls = useAnimation();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Detectar se é dispositivo móvel
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 || 
                 /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
    };
    
    // Verificar no início
    checkMobile();
    
    // Verificar ao redimensionar
    window.addEventListener('resize', checkMobile);
    
    // Iniciar animações
    controls.start({
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        delay: 0.2
      }
    });
    
    return () => window.removeEventListener('resize', checkMobile);
  }, [controls]);

  return (
    <S.HeroSection id="home" className="animated-gradient">
      <S.HeroOverlay />
      {!isMobile && <MatrixEffect />}
      {isMobile && <MobileBackgroundEffect />}
      <S.GridPattern />
      {!isMobile && <TechParticles />}
      <div className="container" style={{ display: 'flex', justifyContent: 'flex-start' }}>
        <S.HeroContent>
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={controls}
            style={{ alignSelf: 'flex-start', width: '100%' }}
          >
            <S.HeroTitle>
              Néri Consultoria<br />
              <GlitchText text="Soluções de TI" delay={1500} /><br />
              Sob Medida para o<br />
              Seu Negócio
            </S.HeroTitle>
          </motion.div>

          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{
              y: 0,
              opacity: 1,
              transition: {
                duration: 0.8,
                delay: 0.4
              }
            }}
            style={{ alignSelf: 'flex-start', width: '100%' }}
          >
            <S.TypedText>
              Tecnologia • Inovação • Excelência
            </S.TypedText>
          </motion.div>

          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{
              y: 0,
              opacity: 1,
              transition: {
                duration: 0.8,
                delay: 0.6
              }
            }}
            style={{ alignSelf: 'flex-start', width: '100%' }}
          >
            <S.HeroSubtitle>
              Transforme sua empresa com nossas soluções personalizadas 
              de tecnologia e alcance novos patamares de eficiência e inovação.
            </S.HeroSubtitle>
          </motion.div>

          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{
              y: 0,
              opacity: 1,
              transition: {
                duration: 0.8,
                delay: 0.8
              }
            }}
            style={{ alignSelf: 'flex-start', width: '100%' }}
          >
            <S.ButtonContainer>
              <S.PrimaryButton>
                <Link 
                  to="services" 
                  spy={true} 
                  smooth={true} 
                  offset={-70} 
                  duration={500}
                >
                  Conheça Nossos Serviços
                </Link>
              </S.PrimaryButton>
              
              <S.SecondaryButton>
                <a
                  href="https://wa.me/5519989830210?text=Olá! Gostaria de saber mais sobre os serviços da Néri Consultoria."
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ textDecoration: 'none', color: 'inherit', display: 'flex', alignItems: 'center' }}
                >
                  Fale Conosco <span className="rotate" style={{ display: 'inline-flex', marginLeft: '5px' }}><>{FaArrowRight({})}</></span>
                </a>
              </S.SecondaryButton>
            </S.ButtonContainer>
          </motion.div>
        </S.HeroContent>
        
        <S.EffectsContainer>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2, duration: 1 }}
          >
            <TerminalEffect />
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.6, duration: 1 }}
          >
            <CodeEffect />
          </motion.div>
        </S.EffectsContainer>
      </div>
      <S.ScrollIndicator>
        <Link 
          to="services" 
          spy={true} 
          smooth={true} 
          offset={-70} 
          duration={500}
        >
          <motion.div
            animate={{
              y: [0, 10, 0],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatType: "loop",
            }}
          >
            <S.ScrollIcon className="pulse">
              <>{FaChevronDown({})}</>
            </S.ScrollIcon>
          </motion.div>
        </Link>
      </S.ScrollIndicator>
    </S.HeroSection>
  );
};

export default Hero; 