import styled from 'styled-components';

interface HeaderContainerProps {
  scrolled: boolean;
}

interface MenuProps {
  isOpen: boolean;
}

interface LogoProps {
  isLoaded: boolean;
}

interface MenuItemProps {
  isLoaded: boolean;
  delayIndex: number;
}

export const HeaderContainer = styled.header<HeaderContainerProps>`
  width: 100%;
  height: 100px;
  position: fixed;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease-in-out;
  background-color: ${({ scrolled }) => 
    scrolled ? 'var(--background-color)' : 'transparent'};
  box-shadow: ${({ scrolled }) => 
    scrolled ? 'var(--box-shadow)' : 'none'};
  color: var(--text-color);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: ${({ scrolled }) => 
      scrolled ? 'blur(10px)' : 'none'};
    z-index: -1;
  }
`;

export const HeaderContent = styled.div`
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  width: 100%;
`;

export const Logo = styled.div<LogoProps>`
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  letter-spacing: -0.5px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 0;
  opacity: ${({ isLoaded }) => isLoaded ? 1 : 0};
  transform: ${({ isLoaded }) => isLoaded ? 'translateY(0)' : 'translateY(-20px)'};
  transition: opacity 0.5s ease, transform 0.5s ease;

  span {
    color: var(--primary-color);
  }

  @media screen and (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

export const LogoImage = styled.img`
  height: 100px;
  width: auto;
  
  @media screen and (max-width: 900px) {
    height: 80px;
  }
`;

export const MobileIcon = styled.div`
  display: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.3s ease;
  position: absolute;
  right: 20px;

  &:hover {
    color: var(--primary-color);
  }

  @media screen and (max-width: 768px) {
    display: flex;
    align-items: center;
  }
`;

export const Menu = styled.ul<MenuProps>`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2rem;

  @media screen and (max-width: 768px) {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    position: absolute;
    top: 100px;
    left: ${({ isOpen }) => (isOpen ? '0' : '-100%')};
    gap: 2rem;
    background-color: var(--background-color);
    transition: all 0.5s ease;
    padding: 3rem 0;
    box-shadow: var(--box-shadow);
  }
`;

export const MenuItem = styled.li<MenuItemProps>`
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
  opacity: ${({ isLoaded }) => isLoaded ? 1 : 0};
  transform: ${({ isLoaded }) => isLoaded ? 'translateY(0)' : 'translateY(20px)'};
  transition: ${({ delayIndex }) => `opacity 0.5s ease ${delayIndex * 0.1}s, transform 0.5s ease ${delayIndex * 0.1}s`};

  &.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
    transform: scaleX(1);
    transition: transform 0.3s ease;
  }

  a {
    color: var(--text-color);
    transition: all 0.3s ease;
    padding: 0.5rem;
    position: relative;
    display: inline-block;

    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--primary-color);
      transform: scaleX(0);
      transform-origin: right;
      transition: transform 0.3s ease;
    }

    &:hover::after {
      transform: scaleX(1);
      transform-origin: left;
    }

    &:hover,
    &.active {
      color: var(--primary-color);
      transform: translateY(-2px);
    }
  }

  @media screen and (max-width: 768px) {
    width: 100%;
    text-align: center;
    font-size: 1.2rem;
    
    &.active::after {
      bottom: -2px;
    }
  }
`; 