import styled from 'styled-components';

export const ProjectsSection = styled.section`
  background-color: var(--background-color);
  padding: 8rem 0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: 0;
  }

  .container {
    position: relative;
    z-index: 1;
  }
`;

export const ProjectsOverlay = styled.div`
  display: none;
`;

export const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: 5rem;
`;

export const SectionTitle = styled.h2`
  font-size: 3rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 1rem;
  color: var(--text-color);
  letter-spacing: -1px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;

  @media (max-width: 768px) {
    font-size: 2.5rem;
    flex-direction: column;
    gap: 10px;
  }
`;

export const SectionSubtitle = styled.p`
  font-size: 1.25rem;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  color: var(--light-text-color);
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1.125rem;
    padding: 0 20px;
  }
`;

export const FeaturedSection = styled.div`
  margin-bottom: 5rem;
`;

export const FeaturedTitle = styled.h3`
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 3rem;
  text-align: center;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 2px;
  }

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

export const FeaturedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
    gap: 2rem;
  }
`;

export const FeaturedCard = styled.div`
  background-color: var(--background-color);
  border: 1px solid var(--gray-200);
  border-radius: 0;
  padding: 3rem 2rem;
  transition: all 0.4s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 4px;
    width: 0;
    background-color: var(--primary-color);
    transition: width 0.4s ease;
  }

  &:hover {
    transform: translateY(-15px);
    box-shadow: var(--box-shadow);
    border-color: transparent;

    &::before {
      width: 100%;
    }
  }
`;

export const ProjectIcon = styled.div`
  background-color: var(--background-color);
  color: var(--primary-color);
  width: 80px;
  height: 80px;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;

  ${FeaturedCard}:hover & {
    background-color: var(--primary-color);
    color: var(--white);
  }
`;

export const ProjectContent = styled.div`
  flex: 1;
  margin-bottom: 1.5rem;
`;

export const ProjectTitle = styled.h4`
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  transition: color 0.3s ease;
`;

export const ProjectDescription = styled.p`
  color: var(--light-text-color);
  line-height: 1.8;
  font-size: 1rem;
  margin-bottom: 1.5rem;
`;

export const TechStack = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 1.5rem;
`;

export const TechTag = styled.span`
  background: var(--gray-100);
  color: var(--text-color);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;

  &:hover {
    background: var(--gray-200);
    transform: translateY(-2px);
  }
`;

export const ProjectLinks = styled.div`
  display: flex;
  gap: 15px;
  justify-content: flex-end;
`;

export const ProjectLink = styled.a`
  background: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.75rem;
  border-radius: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
    z-index: -1;
  }

  &:hover {
    color: var(--white);

    &::before {
      left: 0;
    }
  }
`;

export const FilterSection = styled.div`
  margin-bottom: 3rem;
  text-align: center;
`;

export const FilterButtons = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    gap: 10px;
    padding: 0 20px;
  }
`;

export const FilterButton = styled.button<{ active: boolean }>`
  background: ${props => props.active
    ? 'var(--primary-color)'
    : 'transparent'};
  color: ${props => props.active ? 'var(--white)' : 'var(--primary-color)'};
  border: 1px solid var(--primary-color);
  padding: 0.75rem 1.5rem;
  border-radius: 0;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: ${props => props.active ? '0' : '-100%'};
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
    z-index: -1;
  }

  &:hover {
    color: var(--white);

    &::before {
      left: 0;
    }
  }

  @media (max-width: 768px) {
    padding: 10px 20px;
    font-size: 0.85rem;
  }
`;

export const ProjectsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
    gap: 2rem;
  }
`;

export const ProjectCard = styled.div`
  background-color: var(--background-color);
  border: 1px solid var(--gray-200);
  border-radius: 0;
  padding: 2rem;
  transition: all 0.4s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 4px;
    width: 0;
    background-color: var(--primary-color);
    transition: width 0.4s ease;
  }

  &:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow);
    border-color: transparent;

    &::before {
      width: 100%;
    }
  }
`;

export const ProjectCategory = styled.span`
  background: var(--gray-100);
  color: var(--text-color);
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 15px;
  display: inline-block;
  border: 1px solid var(--gray-200);
`;
